{% extends "base.html" %}

{% block title %}Sitemaps - GSC Controller App{% endblock %}
{% block page_title %}Sitemap Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-sitemap me-2"></i>
                    Submitted Sitemaps
                </h5>
                <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addSitemapModal">
                    <i class="fas fa-plus me-2"></i>
                    Add Sitemap
                </button>
            </div>
            <div class="card-body">
                {% if selected_site %}
                    <div class="mb-3">
                        <strong>Site:</strong> {{ selected_site }}
                    </div>
                    
                    {% if sitemaps %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Sitemap URL</th>
                                        <th>Type</th>
                                        <th>Last Submitted</th>
                                        <th>Status</th>
                                        <th>URLs Submitted</th>
                                        <th>URLs Indexed</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for sitemap in sitemaps %}
                                    <tr>
                                        <td>
                                            <a href="{{ sitemap.path }}" target="_blank" class="text-decoration-none">
                                                {{ sitemap.path }}
                                                <i class="fas fa-external-link-alt ms-1 small"></i>
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ sitemap.type or 'Unknown' }}</span>
                                        </td>
                                        <td>
                                            {% if sitemap.lastSubmitted %}
                                                {{ sitemap.lastSubmitted[:10] }}
                                            {% else %}
                                                <span class="text-muted">Not available</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if sitemap.isPending %}
                                                <span class="badge bg-warning">Pending</span>
                                            {% elif sitemap.isSitemapsIndex %}
                                                <span class="badge bg-info">Index</span>
                                            {% else %}
                                                <span class="badge bg-success">Processed</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if sitemap.contents %}
                                                {{ sitemap.contents[0].submitted or 0 }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if sitemap.contents %}
                                                {{ sitemap.contents[0].indexed or 0 }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-info" onclick="refreshSitemap('{{ sitemap.path }}')">
                                                    <i class="fas fa-sync-alt"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteSitemap('{{ sitemap.path }}')">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-sitemap fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No sitemaps found</h5>
                            <p class="text-muted">Submit your first sitemap to help Google discover your content.</p>
                            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSitemapModal">
                                <i class="fas fa-plus me-2"></i>
                                Add Your First Sitemap
                            </button>
                        </div>
                    {% endif %}
                {% else %}
                    <div class="alert alert-info">
                        <h6>Select a Site</h6>
                        <p class="mb-0">Please select a site from the dashboard to view and manage its sitemaps.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Sitemap Modal -->
<div class="modal fade" id="addSitemapModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New Sitemap</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addSitemapForm">
                    <div class="mb-3">
                        <label for="sitemapUrl" class="form-label">Sitemap URL</label>
                        <input type="url" class="form-control" id="sitemapUrl" name="sitemapUrl" 
                               placeholder="https://example.com/sitemap.xml" required>
                        <div class="form-text">Enter the full URL to your sitemap file.</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitSitemap()">
                    <i class="fas fa-plus me-2"></i>
                    Add Sitemap
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Sitemap Best Practices
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Sitemap Guidelines:</h6>
                        <ul>
                            <li>Use XML format for best compatibility</li>
                            <li>Include only canonical URLs</li>
                            <li>Keep sitemaps under 50MB and 50,000 URLs</li>
                            <li>Update sitemaps when content changes</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Common Sitemap Locations:</h6>
                        <ul>
                            <li>/sitemap.xml</li>
                            <li>/sitemap_index.xml</li>
                            <li>/sitemaps/sitemap.xml</li>
                            <li>Check your robots.txt for sitemap declarations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function submitSitemap() {
    const sitemapUrl = document.getElementById('sitemapUrl').value;
    if (!sitemapUrl) {
        alert('Please enter a sitemap URL');
        return;
    }
    
    // This would submit to the backend API
    alert('Sitemap submission feature will be implemented to add sitemaps via Google Search Console API.');
    
    // Close modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addSitemapModal'));
    modal.hide();
}

function refreshSitemap(sitemapPath) {
    alert('Refresh sitemap feature will be implemented to reprocess sitemaps via Google Search Console API.');
}

function deleteSitemap(sitemapPath) {
    if (confirm('Are you sure you want to delete this sitemap?')) {
        alert('Delete sitemap feature will be implemented to remove sitemaps via Google Search Console API.');
    }
}
</script>
{% endblock %}
