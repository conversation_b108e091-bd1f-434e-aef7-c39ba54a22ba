{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://vpcaccess.googleapis.com/", "batchPath": "batch", "canonicalName": "Serverless VPC Access", "description": "API for managing VPC access connectors.", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/vpc/docs/configure-serverless-vpc-access", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "vpcaccess:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://vpcaccess.mtls.googleapis.com/", "name": "vpcaccess", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"methods": {"list": {"description": "Lists information about the supported locations for this service.", "flatPath": "v1beta1/projects/{projectsId}/locations", "httpMethod": "GET", "id": "vpcaccess.projects.locations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "location": "query", "type": "string"}, "name": {"description": "The resource that owns the locations collection, if applicable.", "location": "path", "pattern": "^projects/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The maximum number of results to return. If not set, the service selects a default.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/locations", "response": {"$ref": "ListLocationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}, "resources": {"connectors": {"methods": {"create": {"description": "Creates a Serverless VPC Access connector, returns an operation.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectors", "httpMethod": "POST", "id": "vpcaccess.projects.locations.connectors.create", "parameterOrder": ["parent"], "parameters": {"connectorId": {"description": "Required. The ID to use for this connector.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project ID and location in which the configuration should be created, specified in the format `projects/*/locations/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/connectors", "request": {"$ref": "Connector"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "delete": {"description": "Deletes a Serverless VPC Access connector. Returns NOT_FOUND if the resource does not exist.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}", "httpMethod": "DELETE", "id": "vpcaccess.projects.locations.connectors.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of a Serverless VPC Access connector to delete.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "get": {"description": "Gets a Serverless VPC Access connector. Returns NOT_FOUND if the resource does not exist.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}", "httpMethod": "GET", "id": "vpcaccess.projects.locations.connectors.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. Name of a Serverless VPC Access connector to get.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Connector"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists Serverless VPC Access connectors.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectors", "httpMethod": "GET", "id": "vpcaccess.projects.locations.connectors.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Maximum number of functions to return per call.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Continuation token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The project and location from which the routes should be listed.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/connectors", "response": {"$ref": "ListConnectorsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "patch": {"description": "Updates a Serverless VPC Access connector, returns an operation.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/connectors/{connectorsId}", "httpMethod": "PATCH", "id": "vpcaccess.projects.locations.connectors.patch", "parameterOrder": ["name"], "parameters": {"name": {"description": "The resource name in the format `projects/*/locations/*/connectors/*`.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/connectors/[^/]+$", "required": true, "type": "string"}, "updateMask": {"description": "The fields to update on the entry group. If absent or empty, all modifiable fields are updated.", "format": "google-fieldmask", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Connector"}, "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}, "operations": {"methods": {"get": {"description": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations/{operationsId}", "httpMethod": "GET", "id": "vpcaccess.projects.locations.operations.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the operation resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+/operations/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Operation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}, "list": {"description": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "flatPath": "v1beta1/projects/{projectsId}/locations/{locationsId}/operations", "httpMethod": "GET", "id": "vpcaccess.projects.locations.operations.list", "parameterOrder": ["name"], "parameters": {"filter": {"description": "The standard list filter.", "location": "query", "type": "string"}, "name": {"description": "The name of the operation's parent resource.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}, "pageSize": {"description": "The standard list page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard list page token.", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}/operations", "response": {"$ref": "ListOperationsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "20231102", "rootUrl": "https://vpcaccess.googleapis.com/", "schemas": {"Connector": {"description": "Definition of a Serverless VPC Access connector.", "id": "Connector", "properties": {"connectedProjects": {"description": "Output only. List of projects using the connector.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "ipCidrRange": {"description": "The range of internal addresses that follows RFC 4632 notation. Example: `**********/28`.", "type": "string"}, "machineType": {"description": "Machine type of VM Instance underlying connector. Default is e2-micro", "type": "string"}, "maxInstances": {"description": "Maximum value of instances in autoscaling group underlying the connector.", "format": "int32", "type": "integer"}, "maxThroughput": {"description": "Maximum throughput of the connector in Mbps. Default is 300, max is 1000. If both max-throughput and max-instances are provided, max-instances takes precedence over max-throughput.", "format": "int32", "type": "integer"}, "minInstances": {"description": "Minimum value of instances in autoscaling group underlying the connector.", "format": "int32", "type": "integer"}, "minThroughput": {"description": "Minimum throughput of the connector in Mbps. Default and min is 200. If both min-throughput and min-instances are provided, min-instances takes precedence over min-throughput.", "format": "int32", "type": "integer"}, "name": {"description": "The resource name in the format `projects/*/locations/*/connectors/*`.", "type": "string"}, "network": {"description": "Name of a VPC network.", "type": "string"}, "state": {"description": "Output only. State of the VPC access connector.", "enum": ["STATE_UNSPECIFIED", "READY", "CREATING", "DELETING", "ERROR", "UPDATING"], "enumDescriptions": ["Invalid state.", "Connector is deployed and ready to receive traffic.", "An Insert operation is in progress. Transient condition.", "A Delete operation is in progress. Transient condition.", "Connector is in a bad state, manual deletion recommended.", "The connector is being updated."], "readOnly": true, "type": "string"}, "subnet": {"$ref": "Subnet", "description": "The subnet in which to house the VPC Access Connector."}}, "type": "object"}, "ListConnectorsResponse": {"description": "Response for listing Serverless VPC Access connectors.", "id": "ListConnectorsResponse", "properties": {"connectors": {"description": "List of Serverless VPC Access connectors.", "items": {"$ref": "Connector"}, "type": "array"}, "nextPageToken": {"description": "Continuation token.", "type": "string"}}, "type": "object"}, "ListLocationsResponse": {"description": "The response message for Locations.ListLocations.", "id": "ListLocationsResponse", "properties": {"locations": {"description": "A list of locations that matches the specified filter in the request.", "items": {"$ref": "Location"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "ListOperationsResponse": {"description": "The response message for Operations.ListOperations.", "id": "ListOperationsResponse", "properties": {"nextPageToken": {"description": "The standard List next-page token.", "type": "string"}, "operations": {"description": "A list of operations that matches the specified filter in the request.", "items": {"$ref": "Operation"}, "type": "array"}}, "type": "object"}, "Location": {"description": "A resource that represents a Google Cloud location.", "id": "Location", "properties": {"displayName": {"description": "The friendly name for this location, typically a nearby city name. For example, \"Tokyo\".", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "Cross-service attributes for the location. For example {\"cloud.googleapis.com/region\": \"us-east1\"}", "type": "object"}, "locationId": {"description": "The canonical id for this location. For example: `\"us-east1\"`.", "type": "string"}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata. For example the available capacity at the given location.", "type": "object"}, "name": {"description": "Resource name for the location, which may vary between implementations. For example: `\"projects/example-project/locations/us-east1\"`", "type": "string"}}, "type": "object"}, "Operation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "Operation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "OperationMetadata": {"description": "Metadata for google.longrunning.Operation.", "id": "OperationMetadata", "properties": {"createTime": {"description": "Output only. Time when the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. Time when the operation completed.", "format": "google-datetime", "readOnly": true, "type": "string"}, "method": {"description": "Output only. Method that initiated the operation e.g. google.cloud.vpcaccess.v1.Connectors.CreateConnector.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Name of the resource that this operation is acting on e.g. projects/my-project/locations/us-central1/connectors/v1.", "readOnly": true, "type": "string"}}, "type": "object"}, "OperationMetadataV1Alpha1": {"description": "Metadata for google.longrunning.Operation.", "id": "OperationMetadataV1Alpha1", "properties": {"endTime": {"description": "Output only. Time when the operation completed.", "format": "google-datetime", "readOnly": true, "type": "string"}, "insertTime": {"description": "Output only. Time when the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "method": {"description": "Output only. Method that initiated the operation e.g. google.cloud.vpcaccess.v1alpha1.Connectors.CreateConnector.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Name of the resource that this operation is acting on e.g. projects/my-project/locations/us-central1/connectors/v1.", "readOnly": true, "type": "string"}}, "type": "object"}, "OperationMetadataV1Beta1": {"description": "Metadata for google.longrunning.Operation.", "id": "OperationMetadataV1Beta1", "properties": {"createTime": {"description": "Output only. Time when the operation was created.", "format": "google-datetime", "readOnly": true, "type": "string"}, "endTime": {"description": "Output only. Time when the operation completed.", "format": "google-datetime", "readOnly": true, "type": "string"}, "method": {"description": "Output only. Method that initiated the operation e.g. google.cloud.vpcaccess.v1beta1.Connectors.CreateConnector.", "readOnly": true, "type": "string"}, "target": {"description": "Output only. Name of the resource that this operation is acting on e.g. projects/my-project/locations/us-central1/connectors/v1.", "readOnly": true, "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "Subnet": {"description": "The subnet in which to house the connector", "id": "Subnet", "properties": {"name": {"description": "Subnet name (relative, not fully qualified). E.g. if the full subnet selfLink is https://compute.googleapis.com/compute/v1/projects/{project}/regions/{region}/subnetworks/{subnetName} the correct input for this field would be {subnetName}", "type": "string"}, "projectId": {"description": "Project in which the subnet exists. If not set, this project is assumed to be the project for which the connector create request was issued.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Serverless VPC Access API", "version": "v1beta1", "version_module": true}