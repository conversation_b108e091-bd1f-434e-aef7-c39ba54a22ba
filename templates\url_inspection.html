{% extends "base.html" %}

{% block title %}URL Inspection - GSC Controller App{% endblock %}
{% block page_title %}URL Inspection & Indexing{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-search me-2"></i>
                    URL Inspection Tool
                </h5>
            </div>
            <div class="card-body">
                <form id="urlInspectionForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="siteUrl" class="form-label">Select Site</label>
                                <select class="form-select" id="siteUrl" name="siteUrl" required>
                                    <option value="">Choose a site...</option>
                                    <!-- Sites will be populated via JavaScript -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="inspectUrl" class="form-label">URL to Inspect</label>
                                <input type="url" class="form-control" id="inspectUrl" name="inspectUrl" 
                                       placeholder="https://example.com/page" required>
                            </div>
                        </div>
                    </div>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-start">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>
                            Inspect URL
                        </button>
                        <button type="button" class="btn btn-success" id="requestIndexing" disabled>
                            <i class="fas fa-plus-circle me-2"></i>
                            Request Indexing
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4" id="resultsSection" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Inspection Results
                </h5>
            </div>
            <div class="card-body" id="resultsContent">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    About URL Inspection
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>What you can do:</h6>
                        <ul>
                            <li>Check if a URL is indexed by Google</li>
                            <li>View crawl information and indexing status</li>
                            <li>Request indexing for new or updated pages</li>
                            <li>See any crawl errors or issues</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6>Tips:</h6>
                        <ul>
                            <li>Use the full URL including https://</li>
                            <li>Make sure the URL belongs to the selected site</li>
                            <li>Indexing requests may take time to process</li>
                            <li>Check back later to see if your page was indexed</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // This would be populated with actual site data from the backend
    // For now, it's a placeholder for the functionality
    
    const form = document.getElementById('urlInspectionForm');
    const requestIndexingBtn = document.getElementById('requestIndexing');
    const resultsSection = document.getElementById('resultsSection');
    const resultsContent = document.getElementById('resultsContent');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        resultsContent.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Inspecting URL...</p>
            </div>
        `;
        resultsSection.style.display = 'block';
        
        // Simulate API call (replace with actual implementation)
        setTimeout(() => {
            resultsContent.innerHTML = `
                <div class="alert alert-info">
                    <h6>URL Inspection Feature</h6>
                    <p>This feature will be implemented to connect with the Google Search Console API to inspect URLs and show detailed indexing information.</p>
                    <p><strong>URL:</strong> ${document.getElementById('inspectUrl').value}</p>
                    <p><strong>Site:</strong> ${document.getElementById('siteUrl').value}</p>
                </div>
            `;
            requestIndexingBtn.disabled = false;
        }, 2000);
    });
    
    requestIndexingBtn.addEventListener('click', function() {
        alert('Indexing request feature will be implemented to submit indexing requests to Google Search Console API.');
    });
});
</script>
{% endblock %}
