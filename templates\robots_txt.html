{% extends "base.html" %}

{% block title %}Robots.txt Management - GSC Controller App{% endblock %}
{% block page_title %}Robots.txt Management{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot me-2"></i>
                    Robots.txt Tester
                </h5>
            </div>
            <div class="card-body">
                <form id="robotsTestForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="siteUrl" class="form-label">Select Site</label>
                                <select class="form-select" id="siteUrl" name="siteUrl" required>
                                    <option value="">Choose a site...</option>
                                    <!-- Sites will be populated via JavaScript -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="testUrl" class="form-label">URL to Test</label>
                                <input type="url" class="form-control" id="testUrl" name="testUrl" 
                                       placeholder="https://example.com/page" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="userAgent" class="form-label">User Agent</label>
                                <select class="form-select" id="userAgent" name="userAgent">
                                    <option value="Googlebot">Googlebot (Desktop)</option>
                                    <option value="Googlebot-Mobile">Googlebot (Mobile)</option>
                                    <option value="Googlebot-News">Googlebot News</option>
                                    <option value="Googlebot-Images">Googlebot Images</option>
                                    <option value="Googlebot-Video">Googlebot Video</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6 d-flex align-items-end">
                            <div class="mb-3 w-100">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-test-tube me-2"></i>
                                    Test Robots.txt
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4" id="resultsSection" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-check me-2"></i>
                    Test Results
                </h5>
            </div>
            <div class="card-body" id="resultsContent">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-file-alt me-2"></i>
                    Current Robots.txt
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="currentRobots" class="form-label">Robots.txt Content</label>
                    <textarea class="form-control" id="currentRobots" rows="10" readonly 
                              placeholder="Load robots.txt content from your site..."></textarea>
                </div>
                <button type="button" class="btn btn-outline-primary" onclick="loadRobotsTxt()">
                    <i class="fas fa-download me-2"></i>
                    Load Current Robots.txt
                </button>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Test Custom Robots.txt
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="customRobots" class="form-label">Custom Robots.txt Content</label>
                    <textarea class="form-control" id="customRobots" rows="10" 
                              placeholder="User-agent: *&#10;Disallow: /admin/&#10;Disallow: /private/&#10;&#10;Sitemap: https://example.com/sitemap.xml"></textarea>
                </div>
                <button type="button" class="btn btn-outline-success" onclick="testCustomRobots()">
                    <i class="fas fa-test-tube me-2"></i>
                    Test Custom Content
                </button>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Robots.txt Guidelines
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6>Basic Syntax:</h6>
                        <ul>
                            <li><code>User-agent: *</code> - Applies to all bots</li>
                            <li><code>Disallow: /path/</code> - Block access to path</li>
                            <li><code>Allow: /path/</code> - Allow access to path</li>
                            <li><code>Sitemap: URL</code> - Declare sitemap location</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>Common Patterns:</h6>
                        <ul>
                            <li><code>Disallow: /admin/</code> - Block admin pages</li>
                            <li><code>Disallow: /*.pdf$</code> - Block PDF files</li>
                            <li><code>Disallow: /*?</code> - Block URLs with parameters</li>
                            <li><code>Crawl-delay: 1</code> - Set crawl delay</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6>Best Practices:</h6>
                        <ul>
                            <li>Place robots.txt at domain root</li>
                            <li>Use specific user-agents when needed</li>
                            <li>Test changes before implementing</li>
                            <li>Include sitemap declarations</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('robotsTestForm');
    const resultsSection = document.getElementById('resultsSection');
    const resultsContent = document.getElementById('resultsContent');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show loading state
        resultsContent.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Testing robots.txt...</p>
            </div>
        `;
        resultsSection.style.display = 'block';
        
        // Simulate API call (replace with actual implementation)
        setTimeout(() => {
            const testUrl = document.getElementById('testUrl').value;
            const userAgent = document.getElementById('userAgent').value;
            
            resultsContent.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle me-2"></i>Test Results</h6>
                    <p><strong>URL:</strong> ${testUrl}</p>
                    <p><strong>User Agent:</strong> ${userAgent}</p>
                    <p><strong>Status:</strong> <span class="badge bg-success">Allowed</span></p>
                    <p class="mb-0"><em>This is a placeholder. The actual implementation will use Google Search Console API to test robots.txt rules.</em></p>
                </div>
            `;
        }, 2000);
    });
});

function loadRobotsTxt() {
    const siteUrl = document.getElementById('siteUrl').value;
    if (!siteUrl) {
        alert('Please select a site first');
        return;
    }
    
    // This would fetch the actual robots.txt from the site
    document.getElementById('currentRobots').value = 'Loading robots.txt content...';
    
    setTimeout(() => {
        document.getElementById('currentRobots').value = 
`User-agent: *
Disallow: /admin/
Disallow: /private/
Allow: /public/

Sitemap: ${siteUrl}/sitemap.xml

# This is placeholder content
# The actual implementation will fetch the real robots.txt file`;
    }, 1000);
}

function testCustomRobots() {
    const customContent = document.getElementById('customRobots').value;
    if (!customContent.trim()) {
        alert('Please enter custom robots.txt content to test');
        return;
    }
    
    alert('Custom robots.txt testing feature will be implemented to test custom content against URLs.');
}
</script>
{% endblock %}
