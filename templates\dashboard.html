{% extends "base.html" %}

{% block title %}Dashboard - GSC Controller App{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-globe me-2"></i>
                    Your Verified Sites
                </h5>
            </div>
            <div class="card-body">
                {% if sites %}
                    <div class="row">
                        {% for site in sites %}
                            <div class="col-md-6 col-lg-4 mb-3">
                                <div class="card border-left-primary">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-grow-1">
                                                <h6 class="card-title mb-1">
                                                    {{ site.siteUrl }}
                                                </h6>
                                                <p class="card-text">
                                                    <small class="text-muted">
                                                        Permission: {{ site.permissionLevel }}
                                                    </small>
                                                </p>
                                            </div>
                                            <div class="ms-2">
                                                {% if site.permissionLevel == 'siteOwner' %}
                                                    <span class="badge bg-success">Owner</span>
                                                {% elif site.permissionLevel == 'siteFullUser' %}
                                                    <span class="badge bg-primary">Full User</span>
                                                {% elif site.permissionLevel == 'siteRestrictedUser' %}
                                                    <span class="badge bg-warning">Restricted</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">{{ site.permissionLevel }}</span>
                                                {% endif %}
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="btn-group btn-group-sm w-100" role="group">
                                                <a href="{{ url_for('url_inspection') }}?site={{ site.siteUrl|urlencode }}" 
                                                   class="btn btn-outline-primary">
                                                    <i class="fas fa-search"></i> Inspect
                                                </a>
                                                <a href="{{ url_for('sitemaps') }}?site={{ site.siteUrl|urlencode }}" 
                                                   class="btn btn-outline-success">
                                                    <i class="fas fa-sitemap"></i> Sitemaps
                                                </a>
                                                <a href="{{ url_for('indexing_performance') }}?site={{ site.siteUrl|urlencode }}" 
                                                   class="btn btn-outline-info">
                                                    <i class="fas fa-chart-line"></i> Stats
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No verified sites found</h5>
                        <p class="text-muted">
                            Make sure you have verified sites in your Google Search Console account.
                        </p>
                        <a href="https://search.google.com/search-console" target="_blank" class="btn btn-primary">
                            <i class="fas fa-external-link-alt me-2"></i>
                            Go to Google Search Console
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-search fa-2x text-primary mb-3"></i>
                <h5 class="card-title">URL Inspection</h5>
                <p class="card-text">Check URL status and request indexing for your pages.</p>
                <a href="{{ url_for('url_inspection') }}" class="btn btn-primary">
                    <i class="fas fa-arrow-right me-2"></i>
                    Start Inspection
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-sitemap fa-2x text-success mb-3"></i>
                <h5 class="card-title">Sitemap Management</h5>
                <p class="card-text">View and manage your submitted sitemaps.</p>
                <a href="{{ url_for('sitemaps') }}" class="btn btn-success">
                    <i class="fas fa-arrow-right me-2"></i>
                    Manage Sitemaps
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-robot fa-2x text-warning mb-3"></i>
                <h5 class="card-title">Robots.txt</h5>
                <p class="card-text">Test and analyze your robots.txt file.</p>
                <a href="{{ url_for('robots_txt') }}" class="btn btn-warning">
                    <i class="fas fa-arrow-right me-2"></i>
                    Check Robots.txt
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
