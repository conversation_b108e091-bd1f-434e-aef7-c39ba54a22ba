{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/doubleclicksearch": {"description": "View and manage your advertising data in DoubleClick Search"}}}}, "basePath": "", "baseUrl": "https://searchads360.googleapis.com/", "batchPath": "batch", "canonicalName": "SA360", "description": "The Search Ads 360 API allows developers to automate downloading reports from Search Ads 360.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/search-ads/reporting", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "searchads360:v0", "kind": "discovery#restDescription", "mtlsRootUrl": "https://searchads360.mtls.googleapis.com/", "name": "searchads360", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"customers": {"methods": {"listAccessibleCustomers": {"description": "Returns resource names of customers directly accessible by the user authenticating the call. List of thrown errors: [AuthenticationError]() [AuthorizationError]() [HeaderError]() [InternalError]() [QuotaError]() [RequestError]()", "flatPath": "v0/customers:listAccessibleCustomers", "httpMethod": "GET", "id": "searchads360.customers.listAccessibleCustomers", "parameterOrder": [], "parameters": {}, "path": "v0/customers:listAccessibleCustomers", "response": {"$ref": "GoogleAdsSearchads360V0Services__ListAccessibleCustomersResponse"}, "scopes": ["https://www.googleapis.com/auth/doubleclicksearch"]}}, "resources": {"customColumns": {"methods": {"get": {"description": "Returns the requested custom column in full detail.", "flatPath": "v0/customers/{customersId}/customColumns/{customColumnsId}", "httpMethod": "GET", "id": "searchads360.customers.customColumns.get", "parameterOrder": ["resourceName"], "parameters": {"resourceName": {"description": "Required. The resource name of the custom column to fetch.", "location": "path", "pattern": "^customers/[^/]+/customColumns/[^/]+$", "required": true, "type": "string"}}, "path": "v0/{+resourceName}", "response": {"$ref": "GoogleAdsSearchads360V0Resources__CustomColumn"}, "scopes": ["https://www.googleapis.com/auth/doubleclicksearch"]}, "list": {"description": "Returns all the custom columns associated with the customer in full detail.", "flatPath": "v0/customers/{customersId}/customColumns", "httpMethod": "GET", "id": "searchads360.customers.customColumns.list", "parameterOrder": ["customerId"], "parameters": {"customerId": {"description": "Required. The ID of the customer to apply the CustomColumn list operation to.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "v0/customers/{+customerId}/customColumns", "response": {"$ref": "GoogleAdsSearchads360V0Services__ListCustomColumnsResponse"}, "scopes": ["https://www.googleapis.com/auth/doubleclicksearch"]}}}, "searchAds360": {"methods": {"search": {"description": "Returns all rows that match the search query. List of thrown errors: [AuthenticationError]() [AuthorizationError]() [HeaderError]() [InternalError]() [QueryError]() [QuotaError]() [RequestError]()", "flatPath": "v0/customers/{customersId}/searchAds360:search", "httpMethod": "POST", "id": "searchads360.customers.searchAds360.search", "parameterOrder": ["customerId"], "parameters": {"customerId": {"description": "Required. The ID of the customer being queried.", "location": "path", "pattern": "^[^/]+$", "required": true, "type": "string"}}, "path": "v0/customers/{+customerId}/searchAds360:search", "request": {"$ref": "GoogleAdsSearchads360V0Services__SearchSearchAds360Request"}, "response": {"$ref": "GoogleAdsSearchads360V0Services__SearchSearchAds360Response"}, "scopes": ["https://www.googleapis.com/auth/doubleclicksearch"]}}}}}, "searchAds360Fields": {"methods": {"get": {"description": "Returns just the requested field. List of thrown errors: [AuthenticationError]() [AuthorizationError]() [HeaderError]() [InternalError]() [QuotaError]() [RequestError]()", "flatPath": "v0/searchAds360Fields/{searchAds360FieldsId}", "httpMethod": "GET", "id": "searchads360.searchAds360Fields.get", "parameterOrder": ["resourceName"], "parameters": {"resourceName": {"description": "Required. The resource name of the field to get.", "location": "path", "pattern": "^searchAds360Fields/[^/]+$", "required": true, "type": "string"}}, "path": "v0/{+resourceName}", "response": {"$ref": "GoogleAdsSearchads360V0Resources__SearchAds360Field"}, "scopes": ["https://www.googleapis.com/auth/doubleclicksearch"]}, "search": {"description": "Returns all fields that match the search query. List of thrown errors: [AuthenticationError]() [AuthorizationError]() [HeaderError]() [InternalError]() [QueryError]() [QuotaError]() [RequestError]()", "flatPath": "v0/searchAds360Fields:search", "httpMethod": "POST", "id": "searchads360.searchAds360Fields.search", "parameterOrder": [], "parameters": {}, "path": "v0/searchAds360Fields:search", "request": {"$ref": "GoogleAdsSearchads360V0Services__SearchSearchAds360FieldsRequest"}, "response": {"$ref": "GoogleAdsSearchads360V0Services__SearchSearchAds360FieldsResponse"}, "scopes": ["https://www.googleapis.com/auth/doubleclicksearch"]}}}}, "revision": "20231106", "rootUrl": "https://searchads360.googleapis.com/", "schemas": {"GoogleAdsSearchads360V0Common__AdScheduleInfo": {"description": "Represents an AdSchedule criterion. AdSchedule is specified as the day of the week and a time interval within which ads will be shown. No more than six AdSchedules can be added for the same day.", "id": "GoogleAdsSearchads360V0Common__AdScheduleInfo", "properties": {"dayOfWeek": {"description": "Day of the week the schedule applies to. This field is required for CREATE operations and is prohibited on UPDATE operations.", "enum": ["UNSPECIFIED", "UNKNOWN", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["Not specified.", "The value is unknown in this version.", "Monday.", "Tuesday.", "Wednesday.", "Thursday.", "Friday.", "Saturday.", "Sunday."], "type": "string"}, "endHour": {"description": "Ending hour in 24 hour time; 24 signifies end of the day. This field must be between 0 and 24, inclusive. This field is required for CREATE operations and is prohibited on UPDATE operations.", "format": "int32", "type": "integer"}, "endMinute": {"description": "Minutes after the end hour at which this schedule ends. The schedule is exclusive of the end minute. This field is required for CREATE operations and is prohibited on UPDATE operations.", "enum": ["UNSPECIFIED", "UNKNOWN", "ZERO", "FIFTEEN", "THIRTY", "FORTY_FIVE"], "enumDescriptions": ["Not specified.", "The value is unknown in this version.", "Zero minutes past the hour.", "Fifteen minutes past the hour.", "Thirty minutes past the hour.", "Forty-five minutes past the hour."], "type": "string"}, "startHour": {"description": "Starting hour in 24 hour time. This field must be between 0 and 23, inclusive. This field is required for CREATE operations and is prohibited on UPDATE operations.", "format": "int32", "type": "integer"}, "startMinute": {"description": "Minutes after the start hour at which this schedule starts. This field is required for CREATE operations and is prohibited on UPDATE operations.", "enum": ["UNSPECIFIED", "UNKNOWN", "ZERO", "FIFTEEN", "THIRTY", "FORTY_FIVE"], "enumDescriptions": ["Not specified.", "The value is unknown in this version.", "Zero minutes past the hour.", "Fifteen minutes past the hour.", "Thirty minutes past the hour.", "Forty-five minutes past the hour."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__AgeRangeInfo": {"description": "An age range criterion.", "id": "GoogleAdsSearchads360V0Common__AgeRangeInfo", "properties": {"type": {"description": "Type of the age range.", "enum": ["UNSPECIFIED", "UNKNOWN", "AGE_RANGE_18_24", "AGE_RANGE_25_34", "AGE_RANGE_35_44", "AGE_RANGE_45_54", "AGE_RANGE_55_64", "AGE_RANGE_65_UP", "AGE_RANGE_UNDETERMINED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Between 18 and 24 years old.", "Between 25 and 34 years old.", "Between 35 and 44 years old.", "Between 45 and 54 years old.", "Between 55 and 64 years old.", "65 years old and beyond.", "Undetermined age range."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__AssetInteractionTarget": {"description": "An AssetInteractionTarget segment.", "id": "GoogleAdsSearchads360V0Common__AssetInteractionTarget", "properties": {"asset": {"description": "The asset resource name.", "type": "string"}, "interactionOnThisAsset": {"description": "Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics. Indicates whether the interaction metrics occurred on the asset itself or a different asset or ad unit.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__AssetUsage": {"description": "Contains the usage information of the asset.", "id": "GoogleAdsSearchads360V0Common__AssetUsage", "properties": {"asset": {"description": "Resource name of the asset.", "type": "string"}, "servedAssetFieldType": {"description": "The served field type of the asset.", "enum": ["UNSPECIFIED", "UNKNOWN", "HEADLINE_1", "HEADLINE_2", "HEADLINE_3", "DESCRIPTION_1", "DESCRIPTION_2", "HEADLINE", "HEADLINE_IN_PORTRAIT", "LONG_HEADLINE", "DESCRIPTION", "DESCRIPTION_IN_PORTRAIT", "BUSINESS_NAME_IN_PORTRAIT", "BUSINESS_NAME", "MARKETING_IMAGE", "MARKETING_IMAGE_IN_PORTRAIT", "SQUARE_MARKETING_IMAGE", "PORTRAIT_MARKETING_IMAGE", "LOGO", "LANDSCAPE_LOGO", "CALL_TO_ACTION", "YOU_TUBE_VIDEO", "SITELINK", "CALL", "MOBILE_APP", "CALLOUT", "STRUCTURED_SNIPPET", "PRICE", "PROMOTION", "AD_IMAGE", "LEAD_FORM", "BUSINESS_LOGO"], "enumDescriptions": ["No value has been specified.", "The received value is not known in this version. This is a response-only value.", "The asset is used in headline 1.", "The asset is used in headline 2.", "The asset is used in headline 3.", "The asset is used in description 1.", "The asset is used in description 2.", "The asset was used in a headline. Use this only if there is only one headline in the ad. Otherwise, use the HEADLINE_1, HEADLINE_2 or HEADLINE_3 enums", "The asset was used as a headline in portrait image.", "The asset was used in a long headline (used in MultiAssetResponsiveAd).", "The asset was used in a description. Use this only if there is only one description in the ad. Otherwise, use the DESCRIPTION_1 or DESCRIPTION_@ enums", "The asset was used as description in portrait image.", "The asset was used as business name in portrait image.", "The asset was used as business name.", "The asset was used as a marketing image.", "The asset was used as a marketing image in portrait image.", "The asset was used as a square marketing image.", "The asset was used as a portrait marketing image.", "The asset was used as a logo.", "The asset was used as a landscape logo.", "The asset was used as a call-to-action.", "The asset was used as a YouTube video.", "This asset is used as a sitelink.", "This asset is used as a call.", "This asset is used as a mobile app.", "This asset is used as a callout.", "This asset is used as a structured snippet.", "This asset is used as a price.", "This asset is used as a promotion.", "This asset is used as an image.", "The asset is used as a lead form.", "The asset is used as a business logo."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__AudienceInfo": {"description": "An audience criterion.", "id": "GoogleAdsSearchads360V0Common__AudienceInfo", "properties": {"audience": {"description": "The Audience resource name.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__BusinessProfileLocation": {"description": "Business Profile location data synced from the linked Business Profile account.", "id": "GoogleAdsSearchads360V0Common__BusinessProfileLocation", "properties": {"labels": {"description": "Advertiser specified label for the location on the Business Profile account. This is synced from the Business Profile account.", "items": {"type": "string"}, "type": "array"}, "listingId": {"description": "Listing ID of this Business Profile location. This is synced from the linked Business Profile account.", "format": "int64", "type": "string"}, "storeCode": {"description": "Business Profile store code of this location. This is synced from the Business Profile account.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__CallToActionAsset": {"description": "A call to action asset.", "id": "GoogleAdsSearchads360V0Common__CallToActionAsset", "properties": {"callToAction": {"description": "Call to action.", "enum": ["UNSPECIFIED", "UNKNOWN", "LEARN_MORE", "GET_QUOTE", "APPLY_NOW", "SIGN_UP", "CONTACT_US", "SUBSCRIBE", "DOWNLOAD", "BOOK_NOW", "SHOP_NOW", "BUY_NOW", "DONATE_NOW", "ORDER_NOW", "PLAY_NOW", "SEE_MORE", "START_NOW", "VISIT_SITE", "WATCH_NOW"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The call to action type is learn more.", "The call to action type is get quote.", "The call to action type is apply now.", "The call to action type is sign up.", "The call to action type is contact us.", "The call to action type is subscribe.", "The call to action type is download.", "The call to action type is book now.", "The call to action type is shop now.", "The call to action type is buy now.", "The call to action type is donate now.", "The call to action type is order now.", "The call to action type is play now.", "The call to action type is see more.", "The call to action type is start now.", "The call to action type is visit site.", "The call to action type is watch now."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__CustomParameter": {"description": "A mapping that can be used by custom parameter tags in a `tracking_url_template`, `final_urls`, or `mobile_final_urls`.", "id": "GoogleAdsSearchads360V0Common__CustomParameter", "properties": {"key": {"description": "The key matching the parameter tag name.", "type": "string"}, "value": {"description": "The value to be substituted.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__DeviceInfo": {"description": "A device criterion.", "id": "GoogleAdsSearchads360V0Common__DeviceInfo", "properties": {"type": {"description": "Type of the device.", "enum": ["UNSPECIFIED", "UNKNOWN", "MOBILE", "TABLET", "DESKTOP", "CONNECTED_TV", "OTHER"], "enumDescriptions": ["Not specified.", "The value is unknown in this version.", "Mobile devices with full browsers.", "Tablets with full browsers.", "Computers.", "Smart TVs and game consoles.", "Other device types."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__EnhancedCpc": {"description": "An automated bidding strategy that raises bids for clicks that seem more likely to lead to a conversion and lowers them for clicks where they seem less likely. This bidding strategy is deprecated and cannot be created anymore. Use ManualCpc with enhanced_cpc_enabled set to true for equivalent functionality.", "id": "GoogleAdsSearchads360V0Common__EnhancedCpc", "properties": {}, "type": "object"}, "GoogleAdsSearchads360V0Common__FrequencyCapEntry": {"description": "A rule specifying the maximum number of times an ad (or some set of ads) can be shown to a user over a particular time period.", "id": "GoogleAdsSearchads360V0Common__FrequencyCapEntry", "properties": {}, "type": "object"}, "GoogleAdsSearchads360V0Common__GenderInfo": {"description": "A gender criterion.", "id": "GoogleAdsSearchads360V0Common__GenderInfo", "properties": {"type": {"description": "Type of the gender.", "enum": ["UNSPECIFIED", "UNKNOWN", "MALE", "FEMALE", "UNDETERMINED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Male.", "Female.", "Undetermined gender."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__ImageAsset": {"description": "An Image asset.", "id": "GoogleAdsSearchads360V0Common__ImageAsset", "properties": {"fileSize": {"description": "File size of the image asset in bytes.", "format": "int64", "type": "string"}, "fullSize": {"$ref": "GoogleAdsSearchads360V0Common__ImageDimension", "description": "Metadata for this image at its original size."}, "mimeType": {"description": "MIME type of the image asset.", "enum": ["UNSPECIFIED", "UNKNOWN", "IMAGE_JPEG", "IMAGE_GIF", "IMAGE_PNG", "FLASH", "TEXT_HTML", "PDF", "MSWORD", "MSEXCEL", "RTF", "AUDIO_WAV", "AUDIO_MP3", "HTML5_AD_ZIP"], "enumDescriptions": ["The mime type has not been specified.", "The received value is not known in this version. This is a response-only value.", "MIME type of image/jpeg.", "MIME type of image/gif.", "MIME type of image/png.", "MIME type of application/x-shockwave-flash.", "MIME type of text/html.", "MIME type of application/pdf.", "MIME type of application/msword.", "MIME type of application/vnd.ms-excel.", "MIME type of application/rtf.", "MIME type of audio/wav.", "MIME type of audio/mp3.", "MIME type of application/x-html5-ad-zip."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__ImageDimension": {"description": "Metadata for an image at a certain size, either original or resized.", "id": "GoogleAdsSearchads360V0Common__ImageDimension", "properties": {"heightPixels": {"description": "Height of the image.", "format": "int64", "type": "string"}, "url": {"description": "A URL that returns the image with this height and width.", "type": "string"}, "widthPixels": {"description": "Width of the image.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__Keyword": {"description": "A Keyword criterion segment.", "id": "GoogleAdsSearchads360V0Common__Keyword", "properties": {"adGroupCriterion": {"description": "The AdGroupCriterion resource name.", "type": "string"}, "info": {"$ref": "GoogleAdsSearchads360V0Common__KeywordInfo", "description": "Keyword info."}}, "type": "object"}, "GoogleAdsSearchads360V0Common__KeywordInfo": {"description": "A keyword criterion.", "id": "GoogleAdsSearchads360V0Common__KeywordInfo", "properties": {"matchType": {"description": "The match type of the keyword.", "enum": ["UNSPECIFIED", "UNKNOWN", "EXACT", "PHRASE", "BROAD"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Exact match.", "Phrase match.", "Broad match."], "type": "string"}, "text": {"description": "The text of the keyword (at most 80 characters and 10 words).", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__LanguageInfo": {"description": "A language criterion.", "id": "GoogleAdsSearchads360V0Common__LanguageInfo", "properties": {"languageConstant": {"description": "The language constant resource name.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__ListingGroupInfo": {"description": "A listing group criterion.", "id": "GoogleAdsSearchads360V0Common__ListingGroupInfo", "properties": {"type": {"description": "Type of the listing group.", "enum": ["UNSPECIFIED", "UNKNOWN", "SUBDIVISION", "UNIT"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Subdivision of products along some listing dimension. These nodes are not used by serving to target listing entries, but is purely to define the structure of the tree.", "Listing group unit that defines a bid."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__LocationGroupInfo": {"description": "A radius around a list of locations specified through a feed.", "id": "GoogleAdsSearchads360V0Common__LocationGroupInfo", "properties": {"feedItemSets": {"description": "FeedItemSets whose FeedItems are targeted. If multiple IDs are specified, then all items that appear in at least one set are targeted. This field cannot be used with geo_target_constants. This is optional and can only be set in CREATE operations.", "items": {"type": "string"}, "type": "array"}, "geoTargetConstants": {"description": "Geo target constant(s) restricting the scope of the geographic area within the feed. Currently only one geo target constant is allowed.", "items": {"type": "string"}, "type": "array"}, "radius": {"description": "Distance in units specifying the radius around targeted locations. This is required and must be set in CREATE operations.", "format": "int64", "type": "string"}, "radiusUnits": {"description": "Unit of the radius. Miles and meters are supported for geo target constants. Milli miles and meters are supported for feed item sets. This is required and must be set in CREATE operations.", "enum": ["UNSPECIFIED", "UNKNOWN", "METERS", "MILES", "MILLI_MILES"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Meters", "<PERSON>", "<PERSON><PERSON>"], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__LocationInfo": {"description": "A location criterion.", "id": "GoogleAdsSearchads360V0Common__LocationInfo", "properties": {"geoTargetConstant": {"description": "The geo target constant resource name.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__ManualCpa": {"description": "Manual bidding strategy that allows advertiser to set the bid per advertiser-specified action.", "id": "GoogleAdsSearchads360V0Common__ManualCpa", "properties": {}, "type": "object"}, "GoogleAdsSearchads360V0Common__ManualCpc": {"description": "Manual click-based bidding where user pays per click.", "id": "GoogleAdsSearchads360V0Common__ManualCpc", "properties": {"enhancedCpcEnabled": {"description": "Whether bids are to be enhanced based on conversion optimizer data.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__ManualCpm": {"description": "Manual impression-based bidding where user pays per thousand impressions.", "id": "GoogleAdsSearchads360V0Common__ManualCpm", "properties": {}, "type": "object"}, "GoogleAdsSearchads360V0Common__MaximizeConversionValue": {"description": "An automated bidding strategy to help get the most conversion value for your campaigns while spending your budget.", "id": "GoogleAdsSearchads360V0Common__MaximizeConversionValue", "properties": {"cpcBidCeilingMicros": {"description": "Maximum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy. Mutable for portfolio bidding strategies only.", "format": "int64", "type": "string"}, "cpcBidFloorMicros": {"description": "Minimum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy. Mutable for portfolio bidding strategies only.", "format": "int64", "type": "string"}, "targetRoas": {"description": "The target return on ad spend (ROAS) option. If set, the bid strategy will maximize revenue while averaging the target return on ad spend. If the target ROAS is high, the bid strategy may not be able to spend the full budget. If the target ROAS is not set, the bid strategy will aim to achieve the highest possible ROAS for the budget.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__MaximizeConversions": {"description": "An automated bidding strategy to help get the most conversions for your campaigns while spending your budget.", "id": "GoogleAdsSearchads360V0Common__MaximizeConversions", "properties": {"cpcBidCeilingMicros": {"description": "Maximum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy. Mutable for portfolio bidding strategies only.", "format": "int64", "type": "string"}, "cpcBidFloorMicros": {"description": "Minimum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy. Mutable for portfolio bidding strategies only.", "format": "int64", "type": "string"}, "targetCpaMicros": {"description": "The target cost-per-action (CPA) option. This is the average amount that you would like to spend per conversion action specified in micro units of the bidding strategy's currency. If set, the bid strategy will get as many conversions as possible at or below the target cost-per-action. If the target CPA is not set, the bid strategy will aim to achieve the lowest possible CPA given the budget.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__Metrics": {"description": "Metrics data.", "id": "GoogleAdsSearchads360V0Common__Metrics", "properties": {"absoluteTopImpressionPercentage": {"description": "The percent of your ad impressions that are shown as the very first ad above the organic search results.", "format": "double", "type": "number"}, "allConversions": {"description": "The total number of conversions. This includes all conversions regardless of the value of include_in_conversions_metric.", "format": "double", "type": "number"}, "allConversionsByConversionDate": {"description": "The total number of conversions. This includes all conversions regardless of the value of include_in_conversions_metric. When this column is selected with date, the values in date column means the conversion date. Details for the by_conversion_date columns are available at https://support.google.com/sa360/answer/9250611.", "format": "double", "type": "number"}, "allConversionsFromClickToCall": {"description": "The number of times people clicked the \"Call\" button to call a store during or after clicking an ad. This number doesn't include whether or not calls were connected, or the duration of any calls. This metric applies to feed items only.", "format": "double", "type": "number"}, "allConversionsFromDirections": {"description": "The number of times people clicked a \"Get directions\" button to navigate to a store after clicking an ad. This metric applies to feed items only.", "format": "double", "type": "number"}, "allConversionsFromInteractionsRate": {"description": "All conversions from interactions (as oppose to view through conversions) divided by the number of ad interactions.", "format": "double", "type": "number"}, "allConversionsFromInteractionsValuePerInteraction": {"description": "The value of all conversions from interactions divided by the total number of interactions.", "format": "double", "type": "number"}, "allConversionsFromMenu": {"description": "The number of times people clicked a link to view a store's menu after clicking an ad. This metric applies to feed items only.", "format": "double", "type": "number"}, "allConversionsFromOrder": {"description": "The number of times people placed an order at a store after clicking an ad. This metric applies to feed items only.", "format": "double", "type": "number"}, "allConversionsFromOtherEngagement": {"description": "The number of other conversions (for example, posting a review or saving a location for a store) that occurred after people clicked an ad. This metric applies to feed items only.", "format": "double", "type": "number"}, "allConversionsFromStoreVisit": {"description": "Estimated number of times people visited a store after clicking an ad. This metric applies to feed items only.", "format": "double", "type": "number"}, "allConversionsFromStoreWebsite": {"description": "The number of times that people were taken to a store's URL after clicking an ad. This metric applies to feed items only.", "format": "double", "type": "number"}, "allConversionsValue": {"description": "The value of all conversions.", "format": "double", "type": "number"}, "allConversionsValueByConversionDate": {"description": "The value of all conversions. When this column is selected with date, the values in date column means the conversion date. Details for the by_conversion_date columns are available at https://support.google.com/sa360/answer/9250611.", "format": "double", "type": "number"}, "allConversionsValuePerCost": {"description": "The value of all conversions divided by the total cost of ad interactions (such as clicks for text ads or views for video ads).", "format": "double", "type": "number"}, "averageCost": {"description": "The average amount you pay per interaction. This amount is the total cost of your ads divided by the total number of interactions.", "format": "double", "type": "number"}, "averageCpc": {"description": "The total cost of all clicks divided by the total number of clicks received. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "double", "type": "number"}, "averageCpm": {"description": "Average cost-per-thousand impressions (CPM). This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "double", "type": "number"}, "clicks": {"description": "The number of clicks.", "format": "int64", "type": "string"}, "clientAccountConversions": {"description": "The number of client account conversions. This only includes conversion actions which include_in_client_account_conversions_metric attribute is set to true. If you use conversion-based bidding, your bid strategies will optimize for these conversions.", "format": "double", "type": "number"}, "clientAccountConversionsValue": {"description": "The value of client account conversions. This only includes conversion actions which include_in_client_account_conversions_metric attribute is set to true. If you use conversion-based bidding, your bid strategies will optimize for these conversions.", "format": "double", "type": "number"}, "clientAccountCrossSellCostOfGoodsSoldMicros": {"description": "Client account cross-sell cost of goods sold (COGS) is the total cost of products sold as a result of advertising a different product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If these products don't match then this is considered cross-sell. Cross-sell cost of goods sold is the total cost of the products sold that weren't advertised. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat has a cost of goods sold value of $3, the shirt has a cost of goods sold value of $5. The cross-sell cost of goods sold for this order is $5. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "clientAccountCrossSellGrossProfitMicros": {"description": "Client account cross-sell gross profit is the profit you made from products sold as a result of advertising a different product, minus cost of goods sold (COGS). How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the purchase is a sold product. If these products don't match then this is considered cross-sell. Cross-sell gross profit is the revenue you made from cross-sell attributed to your ads minus the cost of the goods sold. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The shirt is priced $20 and has a cost of goods sold value of $5. The cross-sell gross profit of this order is $15 = $20 - $5. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "clientAccountCrossSellRevenueMicros": {"description": "Client account cross-sell revenue is the total amount you made from products sold as a result of advertising a different product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If these products don't match then this is considered cross-sell. Cross-sell revenue is the total value you made from cross-sell attributed to your ads. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat is priced $10 and the shirt is priced $20. The cross-sell revenue of this order is $20. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "clientAccountCrossSellUnitsSold": {"description": "Client account cross-sell units sold is the total number of products sold as a result of advertising a different product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If these products don't match then this is considered cross-sell. Cross-sell units sold is the total number of cross-sold products from all orders attributed to your ads. Example: Someone clicked on a Shopping ad for a hat then bought the same hat, a shirt and a jacket. The cross-sell units sold in this order is 2. This metric is only available if you report conversions with cart data.", "format": "double", "type": "number"}, "clientAccountLeadCostOfGoodsSoldMicros": {"description": "Client account lead cost of goods sold (COGS) is the total cost of products sold as a result of advertising the same product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If the advertised and sold products match, then the cost of these goods is counted under lead cost of goods sold. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat has a cost of goods sold value of $3, the shirt has a cost of goods sold value of $5. The lead cost of goods sold for this order is $3. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "clientAccountLeadGrossProfitMicros": {"description": "Client account lead gross profit is the profit you made from products sold as a result of advertising the same product, minus cost of goods sold (COGS). How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If the advertised and sold products match, then the revenue you made from these sales minus the cost of goods sold is your lead gross profit. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat is priced $10 and has a cost of goods sold value of $3. The lead gross profit of this order is $7 = $10 - $3. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "clientAccountLeadRevenueMicros": {"description": "Client account lead revenue is the total amount you made from products sold as a result of advertising the same product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If the advertised and sold products match, then the total value you made from the sales of these products is shown under lead revenue. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat is priced $10 and the shirt is priced $20. The lead revenue of this order is $10. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "clientAccountLeadUnitsSold": {"description": "Client account lead units sold is the total number of products sold as a result of advertising the same product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If the advertised and sold products match, then the total number of these products sold is shown under lead units sold. Example: Someone clicked on a Shopping ad for a hat then bought the same hat, a shirt and a jacket. The lead units sold in this order is 1. This metric is only available if you report conversions with cart data.", "format": "double", "type": "number"}, "clientAccountViewThroughConversions": {"description": "The total number of view-through conversions. These happen when a customer sees an image or rich media ad, then later completes a conversion on your site without interacting with (for example, clicking on) another ad.", "format": "int64", "type": "string"}, "contentBudgetLostImpressionShare": {"description": "The estimated percent of times that your ad was eligible to show on the Display Network but didn't because your budget was too low. Note: Content budget lost impression share is reported in the range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.", "format": "double", "type": "number"}, "contentImpressionShare": {"description": "The impressions you've received on the Display Network divided by the estimated number of impressions you were eligible to receive. Note: Content impression share is reported in the range of 0.1 to 1. Any value below 0.1 is reported as 0.0999.", "format": "double", "type": "number"}, "contentRankLostImpressionShare": {"description": "The estimated percentage of impressions on the Display Network that your ads didn't receive due to poor Ad Rank. Note: Content rank lost impression share is reported in the range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.", "format": "double", "type": "number"}, "conversions": {"description": "The number of conversions. This only includes conversion actions which include_in_conversions_metric attribute is set to true. If you use conversion-based bidding, your bid strategies will optimize for these conversions.", "format": "double", "type": "number"}, "conversionsByConversionDate": {"description": "The sum of conversions by conversion date for biddable conversion types. Can be fractional due to attribution modeling. When this column is selected with date, the values in date column means the conversion date.", "format": "double", "type": "number"}, "conversionsFromInteractionsRate": {"description": "Average biddable conversions (from interaction) per conversion eligible interaction. Shows how often, on average, an ad interaction leads to a biddable conversion.", "format": "double", "type": "number"}, "conversionsFromInteractionsValuePerInteraction": {"description": "The value of conversions from interactions divided by the number of ad interactions. This only includes conversion actions which include_in_conversions_metric attribute is set to true. If you use conversion-based bidding, your bid strategies will optimize for these conversions.", "format": "double", "type": "number"}, "conversionsValue": {"description": "The sum of conversion values for the conversions included in the \"conversions\" field. This metric is useful only if you entered a value for your conversion actions.", "format": "double", "type": "number"}, "conversionsValueByConversionDate": {"description": "The sum of biddable conversions value by conversion date. When this column is selected with date, the values in date column means the conversion date.", "format": "double", "type": "number"}, "conversionsValuePerCost": {"description": "The value of biddable conversion divided by the total cost of conversion eligible interactions.", "format": "double", "type": "number"}, "costMicros": {"description": "The sum of your cost-per-click (CPC) and cost-per-thousand impressions (CPM) costs during this period. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "costPerAllConversions": {"description": "The cost of ad interactions divided by all conversions.", "format": "double", "type": "number"}, "costPerConversion": {"description": "Average conversion eligible cost per biddable conversion.", "format": "double", "type": "number"}, "costPerCurrentModelAttributedConversion": {"description": "The cost of ad interactions divided by current model attributed conversions. This only includes conversion actions which include_in_conversions_metric attribute is set to true. If you use conversion-based bidding, your bid strategies will optimize for these conversions.", "format": "double", "type": "number"}, "crossDeviceConversions": {"description": "Conversions from when a customer clicks on an ad on one device, then converts on a different device or browser. Cross-device conversions are already included in all_conversions.", "format": "double", "type": "number"}, "crossDeviceConversionsValue": {"description": "The sum of the value of cross-device conversions.", "format": "double", "type": "number"}, "crossSellCostOfGoodsSoldMicros": {"description": "Cross-sell cost of goods sold (COGS) is the total cost of products sold as a result of advertising a different product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If these products don't match then this is considered cross-sell. Cross-sell cost of goods sold is the total cost of the products sold that weren't advertised. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat has a cost of goods sold value of $3, the shirt has a cost of goods sold value of $5. The cross-sell cost of goods sold for this order is $5. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "crossSellGrossProfitMicros": {"description": "Cross-sell gross profit is the profit you made from products sold as a result of advertising a different product, minus cost of goods sold (COGS). How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the purchase is a sold product. If these products don't match then this is considered cross-sell. Cross-sell gross profit is the revenue you made from cross-sell attributed to your ads minus the cost of the goods sold. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The shirt is priced $20 and has a cost of goods sold value of $5. The cross-sell gross profit of this order is $15 = $20 - $5. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "crossSellRevenueMicros": {"description": "Cross-sell revenue is the total amount you made from products sold as a result of advertising a different product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If these products don't match then this is considered cross-sell. Cross-sell revenue is the total value you made from cross-sell attributed to your ads. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat is priced $10 and the shirt is priced $20. The cross-sell revenue of this order is $20. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "crossSellUnitsSold": {"description": "Cross-sell units sold is the total number of products sold as a result of advertising a different product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If these products don't match then this is considered cross-sell. Cross-sell units sold is the total number of cross-sold products from all orders attributed to your ads. Example: Someone clicked on a Shopping ad for a hat then bought the same hat, a shirt and a jacket. The cross-sell units sold in this order is 2. This metric is only available if you report conversions with cart data.", "format": "double", "type": "number"}, "ctr": {"description": "The number of clicks your ad receives (Clicks) divided by the number of times your ad is shown (Impressions).", "format": "double", "type": "number"}, "historicalCreativeQualityScore": {"description": "The creative historical quality score.", "enum": ["UNSPECIFIED", "UNKNOWN", "BELOW_AVERAGE", "AVERAGE", "ABOVE_AVERAGE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Quality of the creative is below average.", "Quality of the creative is average.", "Quality of the creative is above average."], "type": "string"}, "historicalLandingPageQualityScore": {"description": "The quality of historical landing page experience.", "enum": ["UNSPECIFIED", "UNKNOWN", "BELOW_AVERAGE", "AVERAGE", "ABOVE_AVERAGE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Quality of the creative is below average.", "Quality of the creative is average.", "Quality of the creative is above average."], "type": "string"}, "historicalQualityScore": {"description": "The historical quality score.", "format": "int64", "type": "string"}, "historicalSearchPredictedCtr": {"description": "The historical search predicted click through rate (CTR).", "enum": ["UNSPECIFIED", "UNKNOWN", "BELOW_AVERAGE", "AVERAGE", "ABOVE_AVERAGE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Quality of the creative is below average.", "Quality of the creative is average.", "Quality of the creative is above average."], "type": "string"}, "impressions": {"description": "Count of how often your ad has appeared on a search results page or website on the Google Network.", "format": "int64", "type": "string"}, "interactionEventTypes": {"description": "The types of payable and free interactions.", "items": {"enum": ["UNSPECIFIED", "UNKNOWN", "CLICK", "ENGAGEMENT", "VIDEO_VIEW", "NONE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Click to site. In most cases, this interaction navigates to an external location, usually the advertiser's landing page. This is also the default InteractionEventType for click events.", "The user's expressed intent to engage with the ad in-place.", "User viewed a video ad.", "The default InteractionEventType for ad conversion events. This is used when an ad conversion row does NOT indicate that the free interactions (for example, the ad conversions) should be 'promoted' and reported as part of the core metrics. These are simply other (ad) conversions."], "type": "string"}, "type": "array"}, "interactionRate": {"description": "How often people interact with your ad after it is shown to them. This is the number of interactions divided by the number of times your ad is shown.", "format": "double", "type": "number"}, "interactions": {"description": "The number of interactions. An interaction is the main user action associated with an ad format-clicks for text and shopping ads, views for video ads, and so on.", "format": "int64", "type": "string"}, "invalidClickRate": {"description": "The percentage of clicks filtered out of your total number of clicks (filtered + non-filtered clicks) during the reporting period.", "format": "double", "type": "number"}, "invalidClicks": {"description": "Number of clicks Google considers illegitimate and doesn't charge you for.", "format": "int64", "type": "string"}, "leadCostOfGoodsSoldMicros": {"description": "Lead cost of goods sold (COGS) is the total cost of products sold as a result of advertising the same product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If the advertised and sold products match, then the cost of these goods is counted under lead cost of goods sold. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat has a cost of goods sold value of $3, the shirt has a cost of goods sold value of $5. The lead cost of goods sold for this order is $3. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "leadGrossProfitMicros": {"description": "Lead gross profit is the profit you made from products sold as a result of advertising the same product, minus cost of goods sold (COGS). How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If the advertised and sold products match, then the revenue you made from these sales minus the cost of goods sold is your lead gross profit. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat is priced $10 and has a cost of goods sold value of $3. The lead gross profit of this order is $7 = $10 - $3. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "leadRevenueMicros": {"description": "Lead revenue is the total amount you made from products sold as a result of advertising the same product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If the advertised and sold products match, then the total value you made from the sales of these products is shown under lead revenue. Example: Someone clicked on a Shopping ad for a hat then bought the same hat and a shirt. The hat is priced $10 and the shirt is priced $20. The lead revenue of this order is $10. This metric is only available if you report conversions with cart data. This metric is a monetary value and returned in the customer's currency by default. See the metrics_currency parameter at https://developers.google.com/search-ads/reporting/query/query-structure#parameters_clause", "format": "int64", "type": "string"}, "leadUnitsSold": {"description": "Lead units sold is the total number of products sold as a result of advertising the same product. How it works: You report conversions with cart data for completed purchases on your website. If the ad that was interacted with before the purchase has an associated product (see Shopping Ads) then this product is considered the advertised product. Any product included in the order the customer places is a sold product. If the advertised and sold products match, then the total number of these products sold is shown under lead units sold. Example: Someone clicked on a Shopping ad for a hat then bought the same hat, a shirt and a jacket. The lead units sold in this order is 1. This metric is only available if you report conversions with cart data.", "format": "double", "type": "number"}, "mobileFriendlyClicksPercentage": {"description": "The percentage of mobile clicks that go to a mobile-friendly page.", "format": "double", "type": "number"}, "searchAbsoluteTopImpressionShare": {"description": "The percentage of the customer's Shopping or Search ad impressions that are shown in the most prominent Shopping position. See https://support.google.com/sa360/answer/9566729 for details. Any value below 0.1 is reported as 0.0999.", "format": "double", "type": "number"}, "searchBudgetLostAbsoluteTopImpressionShare": {"description": "The number estimating how often your ad wasn't the very first ad above the organic search results due to a low budget. Note: Search budget lost absolute top impression share is reported in the range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.", "format": "double", "type": "number"}, "searchBudgetLostImpressionShare": {"description": "The estimated percent of times that your ad was eligible to show on the Search Network but didn't because your budget was too low. Note: Search budget lost impression share is reported in the range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.", "format": "double", "type": "number"}, "searchBudgetLostTopImpressionShare": {"description": "The number estimating how often your ad didn't show anywhere above the organic search results due to a low budget. Note: Search budget lost top impression share is reported in the range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.", "format": "double", "type": "number"}, "searchClickShare": {"description": "The number of clicks you've received on the Search Network divided by the estimated number of clicks you were eligible to receive. Note: Search click share is reported in the range of 0.1 to 1. Any value below 0.1 is reported as 0.0999.", "format": "double", "type": "number"}, "searchExactMatchImpressionShare": {"description": "The impressions you've received divided by the estimated number of impressions you were eligible to receive on the Search Network for search terms that matched your keywords exactly (or were close variants of your keyword), regardless of your keyword match types. Note: Search exact match impression share is reported in the range of 0.1 to 1. Any value below 0.1 is reported as 0.0999.", "format": "double", "type": "number"}, "searchImpressionShare": {"description": "The impressions you've received on the Search Network divided by the estimated number of impressions you were eligible to receive. Note: Search impression share is reported in the range of 0.1 to 1. Any value below 0.1 is reported as 0.0999.", "format": "double", "type": "number"}, "searchRankLostAbsoluteTopImpressionShare": {"description": "The number estimating how often your ad wasn't the very first ad above the organic search results due to poor Ad Rank. Note: Search rank lost absolute top impression share is reported in the range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.", "format": "double", "type": "number"}, "searchRankLostImpressionShare": {"description": "The estimated percentage of impressions on the Search Network that your ads didn't receive due to poor Ad Rank. Note: Search rank lost impression share is reported in the range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.", "format": "double", "type": "number"}, "searchRankLostTopImpressionShare": {"description": "The number estimating how often your ad didn't show anywhere above the organic search results due to poor Ad Rank. Note: Search rank lost top impression share is reported in the range of 0 to 0.9. Any value above 0.9 is reported as 0.9001.", "format": "double", "type": "number"}, "searchTopImpressionShare": {"description": "The impressions you've received in the top location (anywhere above the organic search results) compared to the estimated number of impressions you were eligible to receive in the top location. Note: Search top impression share is reported in the range of 0.1 to 1. Any value below 0.1 is reported as 0.0999.", "format": "double", "type": "number"}, "topImpressionPercentage": {"description": "The percent of your ad impressions that are shown anywhere above the organic search results.", "format": "double", "type": "number"}, "valuePerAllConversions": {"description": "The value of all conversions divided by the number of all conversions.", "format": "double", "type": "number"}, "valuePerAllConversionsByConversionDate": {"description": "The value of all conversions divided by the number of all conversions. When this column is selected with date, the values in date column means the conversion date. Details for the by_conversion_date columns are available at https://support.google.com/sa360/answer/9250611.", "format": "double", "type": "number"}, "valuePerConversion": {"description": "The value of biddable conversion divided by the number of biddable conversions. Shows how much, on average, each of the biddable conversions is worth.", "format": "double", "type": "number"}, "valuePerConversionsByConversionDate": {"description": "Biddable conversions value by conversion date divided by biddable conversions by conversion date. Shows how much, on average, each of the biddable conversions is worth (by conversion date). When this column is selected with date, the values in date column means the conversion date.", "format": "double", "type": "number"}, "visits": {"description": "Clicks that Search Ads 360 has successfully recorded and forwarded to an advertiser's landing page.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__MobileAppAsset": {"description": "An asset representing a mobile app.", "id": "GoogleAdsSearchads360V0Common__MobileAppAsset", "properties": {"appId": {"description": "Required. A string that uniquely identifies a mobile application. It should just contain the platform native id, like \"com.android.ebay\" for Android or \"12345689\" for iOS.", "type": "string"}, "appStore": {"description": "Required. The application store that distributes this specific app.", "enum": ["UNSPECIFIED", "UNKNOWN", "APPLE_APP_STORE", "GOOGLE_APP_STORE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Mobile app vendor for Apple app store.", "Mobile app vendor for Google app store."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__PercentCpc": {"description": "A bidding strategy where bids are a fraction of the advertised price for some good or service.", "id": "GoogleAdsSearchads360V0Common__PercentCpc", "properties": {"cpcBidCeilingMicros": {"description": "Maximum bid limit that can be set by the bid strategy. This is an optional field entered by the advertiser and specified in local micros. Note: A zero value is interpreted in the same way as having bid_ceiling undefined.", "format": "int64", "type": "string"}, "enhancedCpcEnabled": {"description": "Adjusts the bid for each auction upward or downward, depending on the likelihood of a conversion. Individual bids may exceed cpc_bid_ceiling_micros, but the average bid amount for a campaign should not.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__RealTimeBiddingSetting": {"description": "Settings for Real-Time Bidding, a feature only available for campaigns targeting the Ad Exchange network.", "id": "GoogleAdsSearchads360V0Common__RealTimeBiddingSetting", "properties": {"optIn": {"description": "Whether the campaign is opted in to real-time bidding.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__SearchAds360ExpandedDynamicSearchAdInfo": {"description": "An expanded dynamic search ad.", "id": "GoogleAdsSearchads360V0Common__SearchAds360ExpandedDynamicSearchAdInfo", "properties": {"adTrackingId": {"description": "The tracking id of the ad.", "format": "int64", "type": "string"}, "description1": {"description": "The first line of the ad's description.", "type": "string"}, "description2": {"description": "The second line of the ad's description.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__SearchAds360ExpandedTextAdInfo": {"description": "A Search Ads 360 expanded text ad.", "id": "GoogleAdsSearchads360V0Common__SearchAds360ExpandedTextAdInfo", "properties": {"adTrackingId": {"description": "The tracking id of the ad.", "format": "int64", "type": "string"}, "description1": {"description": "The first line of the ad's description.", "type": "string"}, "description2": {"description": "The second line of the ad's description.", "type": "string"}, "headline": {"description": "The headline of the ad.", "type": "string"}, "headline2": {"description": "The second headline of the ad.", "type": "string"}, "headline3": {"description": "The third headline of the ad.", "type": "string"}, "path1": {"description": "Text appended to the auto-generated visible URL with a delimiter.", "type": "string"}, "path2": {"description": "Text appended to path1 with a delimiter.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__SearchAds360ProductAdInfo": {"description": "A Search Ads 360 product ad.", "id": "GoogleAdsSearchads360V0Common__SearchAds360ProductAdInfo", "properties": {}, "type": "object"}, "GoogleAdsSearchads360V0Common__SearchAds360ResponsiveSearchAdInfo": {"description": "A Search Ads 360 responsive search ad.", "id": "GoogleAdsSearchads360V0Common__SearchAds360ResponsiveSearchAdInfo", "properties": {"adTrackingId": {"description": "The tracking id of the ad.", "format": "int64", "type": "string"}, "path1": {"description": "Text appended to the auto-generated visible URL with a delimiter.", "type": "string"}, "path2": {"description": "Text appended to path1 with a delimiter.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__SearchAds360TextAdInfo": {"description": "A Search Ads 360 text ad.", "id": "GoogleAdsSearchads360V0Common__SearchAds360TextAdInfo", "properties": {"adTrackingId": {"description": "The tracking id of the ad.", "format": "int64", "type": "string"}, "description1": {"description": "The first line of the ad's description.", "type": "string"}, "description2": {"description": "The second line of the ad's description.", "type": "string"}, "displayMobileUrl": {"description": "The displayed mobile URL of the ad.", "type": "string"}, "displayUrl": {"description": "The displayed URL of the ad.", "type": "string"}, "headline": {"description": "The headline of the ad.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__Segments": {"description": "Segment only fields.", "id": "GoogleAdsSearchads360V0Common__Segments", "properties": {"adNetworkType": {"description": "Ad network type.", "enum": ["UNSPECIFIED", "UNKNOWN", "SEARCH", "SEARCH_PARTNERS", "CONTENT", "YOUTUBE_SEARCH", "YOUTUBE_WATCH", "MIXED"], "enumDescriptions": ["Not specified.", "The value is unknown in this version.", "Google search.", "Search partners.", "Display Network.", "YouTube Search.", "YouTube Videos", "Cross-network."], "type": "string"}, "assetInteractionTarget": {"$ref": "GoogleAdsSearchads360V0Common__AssetInteractionTarget", "description": "Only used with CustomerAsset, CampaignAsset and AdGroupAsset metrics. Indicates whether the interaction metrics occurred on the asset itself or a different asset or ad unit. Interactions (for example, clicks) are counted across all the parts of the served ad (for example, Ad itself and other components like Sitelinks) when they are served together. When interaction_on_this_asset is true, it means the interactions are on this specific asset and when interaction_on_this_asset is false, it means the interactions is not on this specific asset but on other parts of the served ad this asset is served with."}, "conversionAction": {"description": "Resource name of the conversion action.", "type": "string"}, "conversionActionCategory": {"description": "Conversion action category.", "enum": ["UNSPECIFIED", "UNKNOWN", "DEFAULT", "PAGE_VIEW", "PURCHASE", "SIGNUP", "LEAD", "DOWNLOAD", "ADD_TO_CART", "BEGIN_CHECKOUT", "SUBSCRIBE_PAID", "PHONE_CALL_LEAD", "IMPORTED_LEAD", "SUBMIT_LEAD_FORM", "BOOK_APPOINTMENT", "REQUEST_QUOTE", "GET_DIRECTIONS", "OUTBOUND_CLICK", "CONTACT", "ENGAGEMENT", "STORE_VISIT", "STORE_SALE", "QUALIFIED_LEAD", "CONVERTED_LEAD"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Default category.", "User visiting a page.", "Purchase, sales, or \"order placed\" event.", "Signup user action.", "Lead-generating action.", "Software download action (as for an app).", "The addition of items to a shopping cart or bag on an advertiser site.", "When someone enters the checkout flow on an advertiser site.", "The start of a paid subscription for a product or service.", "A call to indicate interest in an advertiser's offering.", "A lead conversion imported from an external source into Google Ads.", "A submission of a form on an advertiser site indicating business interest.", "A booking of an appointment with an advertiser's business.", "A quote or price estimate request.", "A search for an advertiser's business location with intention to visit.", "A click to an advertiser's partner's site.", "A call, SMS, email, chat or other type of contact to an advertiser.", "A website engagement event such as long site time or a Google Analytics (GA) Smart Goal. Intended to be used for GA, Firebase, GA Gold goal imports.", "A visit to a physical store location.", "A sale occurring in a physical store.", "A lead conversion imported from an external source into Google Ads, that has been further qualified by the advertiser (marketing/sales team). In the lead-to-sale journey, advertisers get leads, then act on them by reaching out to the consumer. If the consumer is interested and may end up buying their product, the advertiser marks such leads as \"qualified leads\".", "A lead conversion imported from an external source into Google Ads, that has further completed a chosen stage as defined by the lead gen advertiser."], "type": "string"}, "conversionActionName": {"description": "Conversion action name.", "type": "string"}, "date": {"description": "Date to which metrics apply. yyyy-MM-dd format, for example, 2018-04-17.", "type": "string"}, "dayOfWeek": {"description": "Day of the week, for example, MONDAY.", "enum": ["UNSPECIFIED", "UNKNOWN", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY", "SUNDAY"], "enumDescriptions": ["Not specified.", "The value is unknown in this version.", "Monday.", "Tuesday.", "Wednesday.", "Thursday.", "Friday.", "Saturday.", "Sunday."], "type": "string"}, "device": {"description": "Device to which metrics apply.", "enum": ["UNSPECIFIED", "UNKNOWN", "MOBILE", "TABLET", "DESKTOP", "CONNECTED_TV", "OTHER"], "enumDescriptions": ["Not specified.", "The value is unknown in this version.", "Mobile devices with full browsers.", "Tablets with full browsers.", "Computers.", "Smart TVs and game consoles.", "Other device types."], "type": "string"}, "keyword": {"$ref": "GoogleAdsSearchads360V0Common__Keyword", "description": "Keyword criterion."}, "month": {"description": "Month as represented by the date of the first day of a month. Formatted as yyyy-MM-dd.", "type": "string"}, "productBiddingCategoryLevel1": {"description": "Bidding category (level 1) of the product.", "type": "string"}, "productBiddingCategoryLevel2": {"description": "Bidding category (level 2) of the product.", "type": "string"}, "productBiddingCategoryLevel3": {"description": "Bidding category (level 3) of the product.", "type": "string"}, "productBiddingCategoryLevel4": {"description": "Bidding category (level 4) of the product.", "type": "string"}, "productBiddingCategoryLevel5": {"description": "Bidding category (level 5) of the product.", "type": "string"}, "productBrand": {"description": "Brand of the product.", "type": "string"}, "productChannel": {"description": "Channel of the product.", "enum": ["UNSPECIFIED", "UNKNOWN", "ONLINE", "LOCAL"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The item is sold online.", "The item is sold in local stores."], "type": "string"}, "productChannelExclusivity": {"description": "Channel exclusivity of the product.", "enum": ["UNSPECIFIED", "UNKNOWN", "SINGLE_CHANNEL", "MULTI_CHANNEL"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The item is sold through one channel only, either local stores or online as indicated by its ProductChannel.", "The item is matched to its online or local stores counterpart, indicating it is available for purchase in both ShoppingProductChannels."], "type": "string"}, "productCondition": {"description": "Condition of the product.", "enum": ["UNSPECIFIED", "UNKNOWN", "OLD", "NEW", "REFURBISHED", "USED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The product condition is old.", "The product condition is new.", "The product condition is refurbished.", "The product condition is used."], "type": "string"}, "productCountry": {"description": "Resource name of the geo target constant for the country of sale of the product.", "type": "string"}, "productCustomAttribute0": {"description": "Custom attribute 0 of the product.", "type": "string"}, "productCustomAttribute1": {"description": "Custom attribute 1 of the product.", "type": "string"}, "productCustomAttribute2": {"description": "Custom attribute 2 of the product.", "type": "string"}, "productCustomAttribute3": {"description": "Custom attribute 3 of the product.", "type": "string"}, "productCustomAttribute4": {"description": "Custom attribute 4 of the product.", "type": "string"}, "productItemId": {"description": "Item ID of the product.", "type": "string"}, "productLanguage": {"description": "Resource name of the language constant for the language of the product.", "type": "string"}, "productSoldBiddingCategoryLevel1": {"description": "Bidding category (level 1) of the product sold.", "type": "string"}, "productSoldBiddingCategoryLevel2": {"description": "Bidding category (level 2) of the product sold.", "type": "string"}, "productSoldBiddingCategoryLevel3": {"description": "Bidding category (level 3) of the product sold.", "type": "string"}, "productSoldBiddingCategoryLevel4": {"description": "Bidding category (level 4) of the product sold.", "type": "string"}, "productSoldBiddingCategoryLevel5": {"description": "Bidding category (level 5) of the product sold.", "type": "string"}, "productSoldBrand": {"description": "Brand of the product sold.", "type": "string"}, "productSoldCondition": {"description": "Condition of the product sold.", "enum": ["UNSPECIFIED", "UNKNOWN", "OLD", "NEW", "REFURBISHED", "USED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The product condition is old.", "The product condition is new.", "The product condition is refurbished.", "The product condition is used."], "type": "string"}, "productSoldCustomAttribute0": {"description": "Custom attribute 0 of the product sold.", "type": "string"}, "productSoldCustomAttribute1": {"description": "Custom attribute 1 of the product sold.", "type": "string"}, "productSoldCustomAttribute2": {"description": "Custom attribute 2 of the product sold.", "type": "string"}, "productSoldCustomAttribute3": {"description": "Custom attribute 3 of the product sold.", "type": "string"}, "productSoldCustomAttribute4": {"description": "Custom attribute 4 of the product sold.", "type": "string"}, "productSoldItemId": {"description": "Item ID of the product sold.", "type": "string"}, "productSoldTitle": {"description": "Title of the product sold.", "type": "string"}, "productSoldTypeL1": {"description": "Type (level 1) of the product sold.", "type": "string"}, "productSoldTypeL2": {"description": "Type (level 2) of the product sold.", "type": "string"}, "productSoldTypeL3": {"description": "Type (level 3) of the product sold.", "type": "string"}, "productSoldTypeL4": {"description": "Type (level 4) of the product sold.", "type": "string"}, "productSoldTypeL5": {"description": "Type (level 5) of the product sold.", "type": "string"}, "productStoreId": {"description": "Store ID of the product.", "type": "string"}, "productTitle": {"description": "Title of the product.", "type": "string"}, "productTypeL1": {"description": "Type (level 1) of the product.", "type": "string"}, "productTypeL2": {"description": "Type (level 2) of the product.", "type": "string"}, "productTypeL3": {"description": "Type (level 3) of the product.", "type": "string"}, "productTypeL4": {"description": "Type (level 4) of the product.", "type": "string"}, "productTypeL5": {"description": "Type (level 5) of the product.", "type": "string"}, "quarter": {"description": "Quarter as represented by the date of the first day of a quarter. Uses the calendar year for quarters, for example, the second quarter of 2018 starts on 2018-04-01. Formatted as yyyy-MM-dd.", "type": "string"}, "week": {"description": "Week as defined as Monday through Sunday, and represented by the date of Monday. Formatted as yyyy-MM-dd.", "type": "string"}, "year": {"description": "Year, formatted as yyyy.", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__TargetCpa": {"description": "An automated bid strategy that sets bids to help get as many conversions as possible at the target cost-per-acquisition (CPA) you set.", "id": "GoogleAdsSearchads360V0Common__TargetCpa", "properties": {"cpcBidCeilingMicros": {"description": "Maximum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy. This should only be set for portfolio bid strategies.", "format": "int64", "type": "string"}, "cpcBidFloorMicros": {"description": "Minimum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy. This should only be set for portfolio bid strategies.", "format": "int64", "type": "string"}, "targetCpaMicros": {"description": "Average CPA target. This target should be greater than or equal to minimum billable unit based on the currency for the account.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__TargetCpm": {"description": "Target CPM (cost per thousand impressions) is an automated bidding strategy that sets bids to optimize performance given the target CPM you set.", "id": "GoogleAdsSearchads360V0Common__TargetCpm", "properties": {}, "type": "object"}, "GoogleAdsSearchads360V0Common__TargetImpressionShare": {"description": "An automated bidding strategy that sets bids so that a certain percentage of search ads are shown at the top of the first page (or other targeted location).", "id": "GoogleAdsSearchads360V0Common__TargetImpressionShare", "properties": {"cpcBidCeilingMicros": {"description": "The highest CPC bid the automated bidding system is permitted to specify. This is a required field entered by the advertiser that sets the ceiling and specified in local micros.", "format": "int64", "type": "string"}, "location": {"description": "The targeted location on the search results page.", "enum": ["UNSPECIFIED", "UNKNOWN", "ANYWHERE_ON_PAGE", "TOP_OF_PAGE", "ABSOLUTE_TOP_OF_PAGE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Any location on the web page.", "Top box of ads.", "Top slot in the top box of ads."], "type": "string"}, "locationFractionMicros": {"description": "The chosen fraction of ads to be shown in the targeted location in micros. For example, 1% equals 10,000.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__TargetOutrankShare": {"description": "An automated bidding strategy that sets bids based on the target fraction of auctions where the advertiser should outrank a specific competitor. This strategy is deprecated.", "id": "GoogleAdsSearchads360V0Common__TargetOutrankShare", "properties": {"cpcBidCeilingMicros": {"description": "Maximum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__TargetRestriction": {"description": "The list of per-targeting-dimension targeting settings.", "id": "GoogleAdsSearchads360V0Common__TargetRestriction", "properties": {"bidOnly": {"description": "Indicates whether to restrict your ads to show only for the criteria you have selected for this targeting_dimension, or to target all values for this targeting_dimension and show ads based on your targeting in other TargetingDimensions. A value of `true` means that these criteria will only apply bid modifiers, and not affect targeting. A value of `false` means that these criteria will restrict targeting as well as applying bid modifiers.", "type": "boolean"}, "targetingDimension": {"description": "The targeting dimension that these settings apply to.", "enum": ["UNSPECIFIED", "UNKNOWN", "KEYWORD", "AUDIENCE", "TOPIC", "GENDER", "AGE_RANGE", "PLACEMENT", "PARENTAL_STATUS", "INCOME_RANGE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Keyword criteria, for example, 'mars cruise'. KEYWORD may be used as a custom bid dimension. Keywords are always a targeting dimension, so may not be set as a target \"ALL\" dimension with TargetRestriction.", "Audience criteria, which include user list, user interest, custom affinity, and custom in market.", "Topic criteria for targeting categories of content, for example, 'category::Animals>Pets' Used for Display and Video targeting.", "Criteria for targeting gender.", "Criteria for targeting age ranges.", "Placement criteria, which include websites like 'www.flowers4sale.com', as well as mobile applications, mobile app categories, YouTube videos, and YouTube channels.", "Criteria for parental status targeting.", "Criteria for income range targeting."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__TargetRoas": {"description": "An automated bidding strategy that helps you maximize revenue while averaging a specific target return on ad spend (ROAS).", "id": "GoogleAdsSearchads360V0Common__TargetRoas", "properties": {"cpcBidCeilingMicros": {"description": "Maximum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy. This should only be set for portfolio bid strategies.", "format": "int64", "type": "string"}, "cpcBidFloorMicros": {"description": "Minimum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy. This should only be set for portfolio bid strategies.", "format": "int64", "type": "string"}, "targetRoas": {"description": "Required. The chosen revenue (based on conversion data) per unit of spend. Value must be between 0.01 and 1000.0, inclusive.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__TargetSpend": {"description": "An automated bid strategy that sets your bids to help get as many clicks as possible within your budget.", "id": "GoogleAdsSearchads360V0Common__TargetSpend", "properties": {"cpcBidCeilingMicros": {"description": "Maximum bid limit that can be set by the bid strategy. The limit applies to all keywords managed by the strategy.", "format": "int64", "type": "string"}, "targetSpendMicros": {"deprecated": true, "description": "The spend target under which to maximize clicks. A TargetSpend bidder will attempt to spend the smaller of this value or the natural throttling spend amount. If not specified, the budget is used as the spend target. This field is deprecated and should no longer be used. See https://ads-developers.googleblog.com/2020/05/reminder-about-sunset-creation-of.html for details.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__TargetingSetting": {"description": "Settings for the targeting-related features, at the campaign and ad group levels. For more details about the targeting setting, visit https://support.google.com/google-ads/answer/7365594", "id": "GoogleAdsSearchads360V0Common__TargetingSetting", "properties": {"targetRestrictions": {"description": "The per-targeting-dimension setting to restrict the reach of your campaign or ad group.", "items": {"$ref": "GoogleAdsSearchads360V0Common__TargetRestriction"}, "type": "array"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__TextAsset": {"description": "A Text asset.", "id": "GoogleAdsSearchads360V0Common__TextAsset", "properties": {"text": {"description": "Text content of the text asset.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__TextLabel": {"description": "A type of label displaying text on a colored background.", "id": "GoogleAdsSearchads360V0Common__TextLabel", "properties": {"backgroundColor": {"description": "Background color of the label in RGB format. This string must match the regular expression '^\\#([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$'. Note: The background color may not be visible for manager accounts.", "type": "string"}, "description": {"description": "A short description of the label. The length must be no more than 200 characters.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__UnifiedCallAsset": {"description": "A unified call asset.", "id": "GoogleAdsSearchads360V0Common__UnifiedCallAsset", "properties": {"adScheduleTargets": {"description": "List of non-overlapping schedules specifying all time intervals for which the asset may serve. There can be a maximum of 6 schedules per day, 42 in total.", "items": {"$ref": "GoogleAdsSearchads360V0Common__AdScheduleInfo"}, "type": "array"}, "callConversionAction": {"description": "The conversion action to attribute a call conversion to. If not set, the default conversion action is used. This field only has effect if call_conversion_reporting_state is set to USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTION.", "type": "string"}, "callConversionReportingState": {"description": "Output only. Indicates whether this CallAsset should use its own call conversion setting, follow the account level setting, or disable call conversion.", "enum": ["UNSPECIFIED", "UNKNOWN", "DISABLED", "USE_ACCOUNT_LEVEL_CALL_CONVERSION_ACTION", "USE_RESOURCE_LEVEL_CALL_CONVERSION_ACTION"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Call conversion action is disabled.", "Call conversion action will use call conversion type set at the account level.", "Call conversion action will use call conversion type set at the resource (call only ads/call extensions) level."], "readOnly": true, "type": "string"}, "callOnly": {"description": "Whether the call only shows the phone number without a link to the website. Applies to Microsoft Ads.", "type": "boolean"}, "callTrackingEnabled": {"description": "Whether the call should be enabled on call tracking. Applies to Microsoft Ads.", "type": "boolean"}, "countryCode": {"description": "Two-letter country code of the phone number. Examples: 'US', 'us'.", "type": "string"}, "endDate": {"description": "Last date of when this asset is effective and still serving, in yyyy-MM-dd format.", "type": "string"}, "phoneNumber": {"description": "The advertiser's raw phone number. Examples: '**********', '(123)456-7890'", "type": "string"}, "startDate": {"description": "Start date of when this asset is effective and can begin serving, in yyyy-MM-dd format.", "type": "string"}, "useSearcherTimeZone": {"description": "Whether to show the call extension in search user's time zone. Applies to Microsoft Ads.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__UnifiedCalloutAsset": {"description": "A unified callout asset.", "id": "GoogleAdsSearchads360V0Common__UnifiedCalloutAsset", "properties": {"adScheduleTargets": {"description": "List of non-overlapping schedules specifying all time intervals for which the asset may serve. There can be a maximum of 6 schedules per day, 42 in total.", "items": {"$ref": "GoogleAdsSearchads360V0Common__AdScheduleInfo"}, "type": "array"}, "calloutText": {"description": "The callout text. The length of this string should be between 1 and 25, inclusive.", "type": "string"}, "endDate": {"description": "Last date of when this asset is effective and still serving, in yyyy-MM-dd format.", "type": "string"}, "startDate": {"description": "Start date of when this asset is effective and can begin serving, in yyyy-MM-dd format.", "type": "string"}, "useSearcherTimeZone": {"description": "Whether to show the asset in search user's time zone. Applies to Microsoft Ads.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__UnifiedLocationAsset": {"description": "A unified location asset.", "id": "GoogleAdsSearchads360V0Common__UnifiedLocationAsset", "properties": {"businessProfileLocations": {"description": "The list of business locations for the customer. This will only be returned if the Location Asset is syncing from the Business Profile account. It is possible to have multiple Business Profile listings under the same account that point to the same Place ID.", "items": {"$ref": "GoogleAdsSearchads360V0Common__BusinessProfileLocation"}, "type": "array"}, "locationOwnershipType": {"description": "The type of location ownership. If the type is BUSINESS_OWNER, it will be served as a location extension. If the type is AFFILIATE, it will be served as an affiliate location.", "enum": ["UNSPECIFIED", "UNKNOWN", "BUSINESS_OWNER", "AFFILIATE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Business Owner of location(legacy location extension - LE).", "Affiliate location(Third party location extension - ALE)."], "type": "string"}, "placeId": {"description": "Place IDs uniquely identify a place in the Google Places database and on Google Maps. This field is unique for a given customer ID and asset type. See https://developers.google.com/places/web-service/place-id to learn more about Place ID.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__UnifiedPageFeedAsset": {"description": "A Unified Page Feed asset.", "id": "GoogleAdsSearchads360V0Common__UnifiedPageFeedAsset", "properties": {"labels": {"description": "Labels used to group the page urls.", "items": {"type": "string"}, "type": "array"}, "pageUrl": {"description": "The webpage that advertisers want to target.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__UnifiedSitelinkAsset": {"description": "A unified sitelink asset.", "id": "GoogleAdsSearchads360V0Common__UnifiedSitelinkAsset", "properties": {"adScheduleTargets": {"description": "List of non-overlapping schedules specifying all time intervals for which the asset may serve. There can be a maximum of 6 schedules per day, 42 in total.", "items": {"$ref": "GoogleAdsSearchads360V0Common__AdScheduleInfo"}, "type": "array"}, "description1": {"description": "First line of the description for the sitelink. If set, the length should be between 1 and 35, inclusive, and description2 must also be set.", "type": "string"}, "description2": {"description": "Second line of the description for the sitelink. If set, the length should be between 1 and 35, inclusive, and description1 must also be set.", "type": "string"}, "endDate": {"description": "Last date of when this asset is effective and still serving, in yyyy-MM-dd format.", "type": "string"}, "linkText": {"description": "URL display text for the sitelink. The length of this string should be between 1 and 25, inclusive.", "type": "string"}, "mobilePreferred": {"description": "Whether the preference is for the sitelink asset to be displayed on mobile devices. Applies to Microsoft Ads.", "type": "boolean"}, "startDate": {"description": "Start date of when this asset is effective and can begin serving, in yyyy-MM-dd format.", "type": "string"}, "trackingId": {"description": "ID used for tracking clicks for the sitelink asset. This is a Yahoo! Japan only field.", "format": "int64", "type": "string"}, "useSearcherTimeZone": {"description": "Whether to show the sitelink asset in search user's time zone. Applies to Microsoft Ads.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__UserListInfo": {"description": "A User List criterion. Represents a user list that is defined by the advertiser to be targeted.", "id": "GoogleAdsSearchads360V0Common__UserListInfo", "properties": {"userList": {"description": "The User List resource name.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__Value": {"description": "A generic data container.", "id": "GoogleAdsSearchads360V0Common__Value", "properties": {"booleanValue": {"description": "A boolean.", "type": "boolean"}, "doubleValue": {"description": "A double.", "format": "double", "type": "number"}, "floatValue": {"description": "A float.", "format": "float", "type": "number"}, "int64Value": {"description": "An int64.", "format": "int64", "type": "string"}, "stringValue": {"description": "A string.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__WebpageConditionInfo": {"description": "Logical expression for targeting webpages of an advertiser's website.", "id": "GoogleAdsSearchads360V0Common__WebpageConditionInfo", "properties": {"argument": {"description": "Argument of webpage targeting condition.", "type": "string"}, "operand": {"description": "Operand of webpage targeting condition.", "enum": ["UNSPECIFIED", "UNKNOWN", "URL", "CATEGORY", "PAGE_TITLE", "PAGE_CONTENT", "CUSTOM_LABEL"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Operand denoting a webpage URL targeting condition.", "Operand denoting a webpage category targeting condition.", "Operand denoting a webpage title targeting condition.", "Operand denoting a webpage content targeting condition.", "Operand denoting a webpage custom label targeting condition."], "type": "string"}, "operator": {"description": "Operator of webpage targeting condition.", "enum": ["UNSPECIFIED", "UNKNOWN", "EQUALS", "CONTAINS"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The argument web condition is equal to the compared web condition.", "The argument web condition is part of the compared web condition."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__WebpageInfo": {"description": "Represents a criterion for targeting webpages of an advertiser's website.", "id": "GoogleAdsSearchads360V0Common__WebpageInfo", "properties": {"conditions": {"description": "Conditions, or logical expressions, for webpage targeting. The list of webpage targeting conditions are and-ed together when evaluated for targeting. An empty list of conditions indicates all pages of the campaign's website are targeted. This field is required for CREATE operations and is prohibited on UPDATE operations.", "items": {"$ref": "GoogleAdsSearchads360V0Common__WebpageConditionInfo"}, "type": "array"}, "coveragePercentage": {"description": "Website criteria coverage percentage. This is the computed percentage of website coverage based on the website target, negative website target and negative keywords in the ad group and campaign. For instance, when coverage returns as 1, it indicates it has 100% coverage. This field is read-only.", "format": "double", "type": "number"}, "criterionName": {"description": "The name of the criterion that is defined by this parameter. The name value will be used for identifying, sorting and filtering criteria with this type of parameters. This field is required for CREATE operations and is prohibited on UPDATE operations.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Common__YoutubeVideoAsset": {"description": "A YouTube asset.", "id": "GoogleAdsSearchads360V0Common__YoutubeVideoAsset", "properties": {"youtubeVideoId": {"description": "YouTube video id. This is the 11 character string value used in the YouTube video URL.", "type": "string"}, "youtubeVideoTitle": {"description": "YouTube video title.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Errors_ErrorLocation_FieldPathElement": {"description": "A part of a field path.", "id": "GoogleAdsSearchads360V0Errors_ErrorLocation_FieldPathElement", "properties": {"fieldName": {"description": "The name of a field or a oneof", "type": "string"}, "index": {"description": "If field_name is a repeated field, this is the element that failed", "format": "int32", "type": "integer"}}, "type": "object"}, "GoogleAdsSearchads360V0Errors__ErrorCode": {"description": "The error reason represented by type and enum.", "id": "GoogleAdsSearchads360V0Errors__ErrorCode", "properties": {"authenticationError": {"description": "Indicates failure to properly authenticate user.", "enum": ["UNSPECIFIED", "UNKNOWN", "AUTHENTICATION_ERROR", "CLIENT_CUSTOMER_ID_INVALID", "CUSTOMER_NOT_FOUND", "GOOGLE_ACCOUNT_DELETED", "GOOGLE_ACCOUNT_COOKIE_INVALID", "GOOGLE_ACCOUNT_AUTHENTICATION_FAILED", "GOOGLE_ACCOUNT_USER_AND_ADS_USER_MISMATCH", "LOGIN_COOKIE_REQUIRED", "NOT_ADS_USER", "OAUTH_TOKEN_INVALID", "OAUTH_TOKEN_EXPIRED", "OAUTH_TOKEN_DISABLED", "OAUTH_TOKEN_REVOKED", "OAUTH_TOKEN_HEADER_INVALID", "LOGIN_COOKIE_INVALID", "USER_ID_INVALID", "TWO_STEP_VERIFICATION_NOT_ENROLLED", "ADVANCED_PROTECTION_NOT_ENROLLED"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "Authentication of the request failed.", "Client customer ID is not a number.", "No customer found for the provided customer ID.", "Client's Google account is deleted.", "Account login token in the cookie is invalid.", "A problem occurred during Google account authentication.", "The user in the Google account login token does not match the user ID in the cookie.", "Login cookie is required for authentication.", "The Google account that generated the OAuth access token is not associated with a Search Ads 360 account. Create a new account, or add the Google account to an existing Search Ads 360 account.", "OAuth token in the header is not valid.", "OAuth token in the header has expired.", "OAuth token in the header has been disabled.", "OAuth token in the header has been revoked.", "OAuth token HTTP header is malformed.", "Login cookie is not valid.", "User ID in the header is not a valid ID.", "An account administrator changed this account's authentication settings. To access this account, enable 2-Step Verification in your Google account at https://www.google.com/landing/2step.", "An account administrator changed this account's authentication settings. To access this account, enable Advanced Protection in your Google account at https://landing.google.com/advancedprotection."], "type": "string"}, "authorizationError": {"description": "An error encountered when trying to authorize a user.", "enum": ["UNSPECIFIED", "UNKNOWN", "USER_PERMISSION_DENIED", "PROJECT_DISABLED", "AUTHORIZATION_ERROR", "ACTION_NOT_PERMITTED", "INCOMPLETE_SIGNUP", "CUSTOMER_NOT_ENABLED", "MISSING_TOS", "INVALID_LOGIN_CUSTOMER_ID_SERVING_CUSTOMER_ID_COMBINATION", "SERVICE_ACCESS_DENIED", "ACCESS_DENIED_FOR_ACCOUNT_TYPE", "METRIC_ACCESS_DENIED"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "User doesn't have permission to access customer. Note: If you're accessing a client customer, the manager's customer ID must be set in the `login-customer-id` header. Learn more at https://developers.google.com/search-ads/reporting/concepts/call-structure#login_customer_id_header", "The Google Cloud project sent in the request does not have permission to access the api.", "Authorization of the client failed.", "The user does not have permission to perform this action (for example, ADD, UPDATE, REMOVE) on the resource or call a method.", "Signup not complete.", "The customer account can't be accessed because it is not yet enabled or has been deactivated.", "The developer must sign the terms of service. They can be found here: https://developers.google.com/terms", "The login customer specified does not have access to the account specified, so the request is invalid.", "The developer specified does not have access to the service.", "The customer (or login customer) isn't allowed in Search Ads 360 API. It belongs to another ads system.", "The developer does not have access to the metrics queried."], "type": "string"}, "customColumnError": {"description": "The reasons for the custom column error", "enum": ["UNSPECIFIED", "UNKNOWN", "CUSTOM_COLUMN_NOT_FOUND", "CUSTOM_COLUMN_NOT_AVAILABLE"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "The custom column has not been found.", "The custom column is not available."], "type": "string"}, "dateError": {"description": "The reasons for the date error", "enum": ["UNSPECIFIED", "UNKNOWN", "INVALID_FIELD_VALUES_IN_DATE", "INVALID_FIELD_VALUES_IN_DATE_TIME", "INVALID_STRING_DATE", "INVALID_STRING_DATE_TIME_MICROS", "INVALID_STRING_DATE_TIME_SECONDS", "INVALID_STRING_DATE_TIME_SECONDS_WITH_OFFSET", "EARLIER_THAN_MINIMUM_DATE", "LATER_THAN_MAXIMUM_DATE", "DATE_RANGE_MINIMUM_DATE_LATER_THAN_MAXIMUM_DATE", "DATE_RANGE_MINIMUM_AND_MAXIMUM_DATES_BOTH_NULL"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "Given field values do not correspond to a valid date.", "Given field values do not correspond to a valid date time.", "The string date's format should be yyyy-mm-dd.", "The string date time's format should be yyyy-mm-dd hh:mm:ss.ssssss.", "The string date time's format should be yyyy-mm-dd hh:mm:ss.", "The string date time's format should be yyyy-mm-dd hh:mm:ss+|-hh:mm.", "Date is before allowed minimum.", "Date is after allowed maximum.", "Date range bounds are not in order.", "Both dates in range are null."], "type": "string"}, "dateRangeError": {"description": "The reasons for the date range error", "enum": ["UNSPECIFIED", "UNKNOWN", "INVALID_DATE", "START_DATE_AFTER_END_DATE", "CANNOT_SET_DATE_TO_PAST", "AFTER_MAXIMUM_ALLOWABLE_DATE", "CANNOT_MODIFY_START_DATE_IF_ALREADY_STARTED"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "Invalid date.", "The start date was after the end date.", "Cannot set date to past time", "A date was used that is past the system \"last\" date.", "Trying to change start date on a resource that has started."], "type": "string"}, "distinctError": {"description": "The reasons for the distinct error", "enum": ["UNSPECIFIED", "UNKNOWN", "DUPLICATE_ELEMENT", "DUPLICATE_TYPE"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "Duplicate element.", "Duplicate type."], "type": "string"}, "headerError": {"description": "The reasons for the header error.", "enum": ["UNSPECIFIED", "UNKNOWN", "INVALID_USER_SELECTED_CUSTOMER_ID", "INVALID_LOGIN_CUSTOMER_ID"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "The user selected customer ID could not be validated.", "The login customer ID could not be validated."], "type": "string"}, "internalError": {"description": "An unexpected server-side error.", "enum": ["UNSPECIFIED", "UNKNOWN", "INTERNAL_ERROR", "ERROR_CODE_NOT_PUBLISHED", "TRANSIENT_ERROR", "DEADLINE_EXCEEDED"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "API encountered unexpected internal error.", "The intended error code doesn't exist in specified API version. It will be released in a future API version.", "API encountered an unexpected transient error. The user should retry their request in these cases.", "The request took longer than a deadline."], "type": "string"}, "invalidParameterError": {"description": "The reasons for invalid parameter errors.", "enum": ["UNSPECIFIED", "UNKNOWN", "INVALID_CURRENCY_CODE"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "The specified currency code is invalid."], "type": "string"}, "queryError": {"description": "An error with the query", "enum": ["UNSPECIFIED", "UNKNOWN", "QUERY_ERROR", "BAD_ENUM_CONSTANT", "BAD_ESCAPE_SEQUENCE", "BAD_FIELD_NAME", "BAD_LIMIT_VALUE", "BAD_NUMBER", "BAD_OPERATOR", "BAD_PARAMETER_NAME", "BAD_PARAMETER_VALUE", "BAD_RESOURCE_TYPE_IN_FROM_CLAUSE", "BAD_SYMBOL", "BAD_VALUE", "DATE_RANGE_TOO_WIDE", "DATE_RANGE_TOO_NARROW", "EXPECTED_AND", "EXPECTED_BY", "EXPECTED_DIMENSION_FIELD_IN_SELECT_CLAUSE", "EXPECTED_FILTERS_ON_DATE_RANGE", "EXPECTED_FROM", "EXPECTED_LIST", "EXPECTED_REFERENCED_FIELD_IN_SELECT_CLAUSE", "EXPECTED_SELECT", "EXPECTED_SINGLE_VALUE", "EXPECTED_VALUE_WITH_BETWEEN_OPERATOR", "INVALID_DATE_FORMAT", "MISALIGNED_DATE_FOR_FILTER", "INVALID_STRING_VALUE", "INVALID_VALUE_WITH_BETWEEN_OPERATOR", "INVALID_VALUE_WITH_DURING_OPERATOR", "INVALID_VALUE_WITH_LIKE_OPERATOR", "OPERATOR_FIELD_MISMATCH", "PROHIBITED_EMPTY_LIST_IN_CONDITION", "PROHIBITED_ENUM_CONSTANT", "PROHIBITED_FIELD_COMBINATION_IN_SELECT_CLAUSE", "PROHIBITED_FIELD_IN_ORDER_BY_CLAUSE", "PROHIBITED_FIELD_IN_SELECT_CLAUSE", "PROHIBITED_FIELD_IN_WHERE_CLAUSE", "PROHIBITED_RESOURCE_TYPE_IN_FROM_CLAUSE", "PROHIBITED_RESOURCE_TYPE_IN_SELECT_CLAUSE", "PROHIBITED_RESOURCE_TYPE_IN_WHERE_CLAUSE", "PROHIBITED_METRIC_IN_SELECT_OR_WHERE_CLAUSE", "PROHIBITED_SEGMENT_IN_SELECT_OR_WHERE_CLAUSE", "PROHIBITED_SEGMENT_WITH_METRIC_IN_SELECT_OR_WHERE_CLAUSE", "LIMIT_VALUE_TOO_LOW", "PROHIBITED_NEWLINE_IN_STRING", "PROHIBITED_VALUE_COMBINATION_IN_LIST", "PROHIBITED_VALUE_COMBINATION_WITH_BETWEEN_OPERATOR", "STRING_NOT_TERMINATED", "TOO_MANY_SEGMENTS", "UNEXPECTED_END_OF_QUERY", "UNEXPECTED_FROM_CLAUSE", "UNRECOGNIZED_FIELD", "UNEXPECTED_INPUT", "REQUESTED_METRICS_FOR_MANAGER", "FILTER_HAS_TOO_MANY_VALUES"], "enumDescriptions": ["Name unspecified.", "The received error code is not known in this version.", "Returned if all other query error reasons are not applicable.", "A condition used in the query references an invalid enum constant.", "Query contains an invalid escape sequence.", "Field name is invalid.", "Limit value is invalid (for example, not a number)", "Encountered number can not be parsed.", "Invalid operator encountered.", "Parameter unknown or not supported.", "Parameter have invalid value.", "Invalid resource type was specified in the FROM clause.", "Non-ASCII symbol encountered outside of strings.", "Value is invalid.", "Date filters fail to restrict date to a range smaller than 31 days. Applicable if the query is segmented by date.", "Filters on date/week/month/quarter have a start date after end date.", "Expected AND between values with BETWEEN operator.", "Expecting ORDER BY to have BY.", "There was no dimension field selected.", "Missing filters on date related fields.", "Missing FROM clause.", "The operator used in the conditions requires the value to be a list.", "Fields used in WHERE or ORDER BY clauses are missing from the SELECT clause.", "SELECT is missing at the beginning of query.", "A list was passed as a value to a condition whose operator expects a single value.", "Missing one or both values with BETWEEN operator.", "Invalid date format. Expected 'YYYY-MM-DD'.", "Misaligned date value for the filter. The date should be the start of a week/month/quarter if the filtered field is segments.week/segments.month/segments.quarter.", "Value passed was not a string when it should have been. For example, it was a number or unquoted literal.", "A String value passed to the BETWEEN operator does not parse as a date.", "The value passed to the DURING operator is not a Date range literal", "A value was passed to the LIKE operator.", "An operator was provided that is inapplicable to the field being filtered.", "A Condition was found with an empty list.", "A condition used in the query references an unsupported enum constant.", "Fields that are not allowed to be selected together were included in the SELECT clause.", "A field that is not orderable was included in the ORDER BY clause.", "A field that is not selectable was included in the SELECT clause.", "A field that is not filterable was included in the WHERE clause.", "Resource type specified in the FROM clause is not supported by this service.", "A field that comes from an incompatible resource was included in the SELECT clause.", "A field that comes from an incompatible resource was included in the WHERE clause.", "A metric incompatible with the main resource or other selected segmenting resources was included in the SELECT or WHERE clause.", "A segment incompatible with the main resource or other selected segmenting resources was included in the SELECT or WHERE clause.", "A segment in the SELECT clause is incompatible with a metric in the SELECT or WHERE clause.", "The value passed to the limit clause is too low.", "Query has a string containing a newline character.", "List contains values of different types.", "The values passed to the BETWEEN operator are not of the same type.", "Query contains unterminated string.", "Too many segments are specified in SELECT clause.", "Query is incomplete and cannot be parsed.", "FROM clause cannot be specified in this query.", "Query contains one or more unrecognized fields.", "Query has an unexpected extra part.", "Metrics cannot be requested for a manager account. To retrieve metrics, issue separate requests against each client account under the manager account.", "The number of values (right-hand-side operands) in a filter exceeds the limit."], "type": "string"}, "quotaError": {"description": "An error with the amount of quota remaining.", "enum": ["UNSPECIFIED", "UNKNOWN", "RESOURCE_EXHAUSTED", "RESOURCE_TEMPORARILY_EXHAUSTED"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "Too many requests.", "Too many requests in a short amount of time."], "type": "string"}, "requestError": {"description": "An error caused by the request", "enum": ["UNSPECIFIED", "UNKNOWN", "RESOURCE_NAME_MISSING", "RESOURCE_NAME_MALFORMED", "BAD_RESOURCE_ID", "INVALID_PRODUCT_NAME", "INVALID_CUSTOMER_ID", "OPERATION_REQUIRED", "RESOURCE_NOT_FOUND", "INVALID_PAGE_TOKEN", "EXPIRED_PAGE_TOKEN", "INVALID_PAGE_SIZE", "REQUIRED_FIELD_MISSING", "IMMUTABLE_FIELD", "TOO_MANY_MUTATE_OPERATIONS", "CANNOT_BE_EXECUTED_BY_MANAGER_ACCOUNT", "CANNOT_MODIFY_FOREIGN_FIELD", "INVALID_ENUM_VALUE", "LOGIN_CUSTOMER_ID_PARAMETER_MISSING", "LOGIN_OR_LINKED_CUSTOMER_ID_PARAMETER_REQUIRED", "VALIDATE_ONLY_REQUEST_HAS_PAGE_TOKEN", "CANNOT_RETURN_SUMMARY_ROW_FOR_REQUEST_WITHOUT_METRICS", "CANNOT_RETURN_SUMMARY_ROW_FOR_VALIDATE_ONLY_REQUESTS", "INCONSISTENT_RETURN_SUMMARY_ROW_VALUE", "TOTAL_RESULTS_COUNT_NOT_ORIGINALLY_REQUESTED", "RPC_DEADLINE_TOO_SHORT", "PRODUCT_NOT_SUPPORTED"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "Resource name is required for this request.", "Resource name provided is malformed.", "Resource name provided is malformed.", "Product name is invalid.", "Customer ID is invalid.", "Mutate operation should have either create, update, or remove specified.", "Requested resource not found.", "Next page token specified in user request is invalid.", "Next page token specified in user request has expired.", "Page size specified in user request is invalid.", "Required field is missing.", "The field cannot be modified because it's immutable. It's also possible that the field can be modified using 'create' operation but not 'update'.", "Received too many entries in request.", "Request cannot be executed by a manager account.", "Mutate request was attempting to modify a readonly field. For instance, Budget fields can be requested for Ad Group, but are read-only for adGroups:mutate.", "Enum value is not permitted.", "The login-customer-id parameter is required for this request.", "Either login-customer-id or linked-customer-id parameter is required for this request.", "page_token is set in the validate only request", "return_summary_row cannot be enabled if request did not select any metrics field.", "return_summary_row should not be enabled for validate only requests.", "return_summary_row parameter value should be the same between requests with page_token field set and their original request.", "The total results count cannot be returned if it was not requested in the original request.", "Deadline specified by the client was too short.", "The product associated with the request is not supported for the current request."], "type": "string"}, "sizeLimitError": {"description": "The reasons for the size limit error", "enum": ["UNSPECIFIED", "UNKNOWN", "REQUEST_SIZE_LIMIT_EXCEEDED", "RESPONSE_SIZE_LIMIT_EXCEEDED"], "enumDescriptions": ["Enum unspecified.", "The received error code is not known in this version.", "The number of entries in the request exceeds the system limit, or the contents of the operations exceed transaction limits due to their size or complexity. Try reducing the number of entries per request.", "The number of entries in the response exceeds the system limit."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Errors__ErrorDetails": {"description": "Additional error details.", "id": "GoogleAdsSearchads360V0Errors__ErrorDetails", "properties": {"quotaErrorDetails": {"$ref": "GoogleAdsSearchads360V0Errors__QuotaErrorDetails", "description": "Details on the quota error, including the scope (account or developer), the rate bucket name and the retry delay."}, "unpublishedErrorCode": {"description": "The error code that should have been returned, but wasn't. This is used when the error code is not published in the client specified version.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Errors__ErrorLocation": {"description": "Describes the part of the request proto that caused the error.", "id": "GoogleAdsSearchads360V0Errors__ErrorLocation", "properties": {"fieldPathElements": {"description": "A field path that indicates which field was invalid in the request.", "items": {"$ref": "GoogleAdsSearchads360V0Errors_ErrorLocation_FieldPathElement"}, "type": "array"}}, "type": "object"}, "GoogleAdsSearchads360V0Errors__QuotaErrorDetails": {"description": "Additional quota error details when there is <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "id": "GoogleAdsSearchads360V0Errors__QuotaErrorDetails", "properties": {"rateName": {"description": "The high level description of the quota bucket. Examples are \"Get requests for standard access\" or \"Requests per account\".", "type": "string"}, "rateScope": {"description": "The rate scope of the quota limit.", "enum": ["UNSPECIFIED", "UNKNOWN", "ACCOUNT", "DEVELOPER"], "enumDescriptions": ["Unspecified enum", "Used for return value only. Represents value unknown in this version.", "Per customer account quota", "Per project quota"], "type": "string"}, "retryDelay": {"description": "Backoff period that customers should wait before sending next request.", "format": "google-duration", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Errors__SearchAds360Error": {"description": "SearchAds360-specific error.", "id": "GoogleAdsSearchads360V0Errors__SearchAds360Error", "properties": {"details": {"$ref": "GoogleAdsSearchads360V0Errors__ErrorDetails", "description": "Additional error details, which are returned by certain error codes. Most error codes do not include details."}, "errorCode": {"$ref": "GoogleAdsSearchads360V0Errors__ErrorCode", "description": "An enum value that indicates which error occurred."}, "location": {"$ref": "GoogleAdsSearchads360V0Errors__ErrorLocation", "description": "Describes the part of the request proto that caused the error."}, "message": {"description": "A human-readable description of the error.", "type": "string"}, "trigger": {"$ref": "GoogleAdsSearchads360V0Common__Value", "description": "The value that triggered the error."}}, "type": "object"}, "GoogleAdsSearchads360V0Errors__SearchAds360Failure": {"description": "Describes how a Search Ads 360 API call failed. It's returned inside google.rpc.Status.details when a call fails.", "id": "GoogleAdsSearchads360V0Errors__SearchAds360Failure", "properties": {"errors": {"description": "The list of errors that occurred.", "items": {"$ref": "GoogleAdsSearchads360V0Errors__SearchAds360Error"}, "type": "array"}, "requestId": {"description": "The unique ID of the request that is used for debugging purposes.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_AdGroupCriterion_PositionEstimates": {"description": "Estimates for criterion bids at various positions.", "id": "GoogleAdsSearchads360V0Resources_AdGroupCriterion_PositionEstimates", "properties": {"topOfPageCpcMicros": {"description": "Output only. The estimate of the CPC bid required for ad to be displayed at the top of the first page of search results.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_AdGroupCriterion_QualityInfo": {"description": "A container for ad group criterion quality information.", "id": "GoogleAdsSearchads360V0Resources_AdGroupCriterion_QualityInfo", "properties": {"qualityScore": {"description": "Output only. The quality score. This field may not be populated if Google does not have enough information to determine a value.", "format": "int32", "readOnly": true, "type": "integer"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_Campaign_DynamicSearchAdsSetting": {"description": "The setting for controlling Dynamic Search Ads (DSA).", "id": "GoogleAdsSearchads360V0Resources_Campaign_DynamicSearchAdsSetting", "properties": {"domainName": {"description": "Required. The Internet domain name that this setting represents, for example, \"google.com\" or \"www.google.com\".", "type": "string"}, "languageCode": {"description": "Required. The language code specifying the language of the domain, for example, \"en\".", "type": "string"}, "useSuppliedUrlsOnly": {"description": "Whether the campaign uses advertiser supplied URLs exclusively.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_Campaign_GeoTargetTypeSetting": {"description": "Represents a collection of settings related to ads geotargeting.", "id": "GoogleAdsSearchads360V0Resources_Campaign_GeoTargetTypeSetting", "properties": {"negativeGeoTargetType": {"description": "The setting used for negative geotargeting in this particular campaign.", "enum": ["UNSPECIFIED", "UNKNOWN", "PRESENCE_OR_INTEREST", "PRESENCE"], "enumDescriptions": ["Not specified.", "The value is unknown in this version.", "Specifies that a user is excluded from seeing the ad if they are in, or show interest in, advertiser's excluded locations.", "Specifies that a user is excluded from seeing the ad if they are in advertiser's excluded locations."], "type": "string"}, "positiveGeoTargetType": {"description": "The setting used for positive geotargeting in this particular campaign.", "enum": ["UNSPECIFIED", "UNKNOWN", "PRESENCE_OR_INTEREST", "SEARCH_INTEREST", "PRESENCE"], "enumDescriptions": ["Not specified.", "The value is unknown in this version.", "Specifies that an ad is triggered if the user is in, or shows interest in, advertiser's targeted locations.", "Specifies that an ad is triggered if the user searches for advertiser's targeted locations. This can only be used with Search and standard Shopping campaigns.", "Specifies that an ad is triggered if the user is in or regularly in advertiser's targeted locations."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_Campaign_NetworkSettings": {"description": "The network settings for the campaign.", "id": "GoogleAdsSearchads360V0Resources_Campaign_NetworkSettings", "properties": {"targetContentNetwork": {"description": "Whether ads will be served on specified placements in the Google Display Network. Placements are specified using the Placement criterion.", "type": "boolean"}, "targetGoogleSearch": {"description": "Whether ads will be served with google.com search results.", "type": "boolean"}, "targetPartnerSearchNetwork": {"description": "Whether ads will be served on the Google Partner Network. This is available only to some select Google partner accounts.", "type": "boolean"}, "targetSearchNetwork": {"description": "Whether ads will be served on partner sites in the Google Search Network (requires `target_google_search` to also be `true`).", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_Campaign_OptimizationGoalSetting": {"description": "Optimization goal setting for this campaign, which includes a set of optimization goal types.", "id": "GoogleAdsSearchads360V0Resources_Campaign_OptimizationGoalSetting", "properties": {"optimizationGoalTypes": {"description": "The list of optimization goal types.", "items": {"enum": ["UNSPECIFIED", "UNKNOWN", "CALL_CLICKS", "DRIVING_DIRECTIONS", "APP_PRE_REGISTRATION"], "enumDescriptions": ["Not specified.", "Used as a return value only. Represents value unknown in this version.", "Optimize for call clicks. Call click conversions are times people selected 'Call' to contact a store after viewing an ad.", "Optimize for driving directions. Driving directions conversions are times people selected 'Get directions' to navigate to a store after viewing an ad.", "Optimize for pre-registration. Pre-registration conversions are the number of pre-registration signups to receive a notification when the app is released."], "type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_Campaign_SelectiveOptimization": {"description": "Selective optimization setting for this campaign, which includes a set of conversion actions to optimize this campaign towards. This feature only applies to app campaigns that use MULTI_CHANNEL as AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as AdvertisingChannelSubType.", "id": "GoogleAdsSearchads360V0Resources_Campaign_SelectiveOptimization", "properties": {"conversionActions": {"description": "The selected set of conversion actions for optimizing this campaign.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_Campaign_ShoppingSetting": {"description": "The setting for Shopping campaigns. Defines the universe of products that can be advertised by the campaign, and how this campaign interacts with other Shopping campaigns.", "id": "GoogleAdsSearchads360V0Resources_Campaign_ShoppingSetting", "properties": {"campaignPriority": {"description": "Priority of the campaign. Campaigns with numerically higher priorities take precedence over those with lower priorities. This field is required for Shopping campaigns, with values between 0 and 2, inclusive. This field is optional for Smart Shopping campaigns, but must be equal to 3 if set.", "format": "int32", "type": "integer"}, "enableLocal": {"description": "Whether to include local products.", "type": "boolean"}, "feedLabel": {"description": "Feed label of products to include in the campaign. Only one of feed_label or sales_country can be set. If used instead of sales_country, the feed_label field accepts country codes in the same format for example: 'XX'. Otherwise can be any string used for feed label in Google Merchant Center.", "type": "string"}, "merchantId": {"description": "Immutable. ID of the Merchant Center account. This field is required for create operations. This field is immutable for Shopping campaigns.", "format": "int64", "type": "string"}, "salesCountry": {"description": "Sales country of products to include in the campaign. ", "type": "string"}, "useVehicleInventory": {"description": "Immutable. Whether to target Vehicle Listing inventory.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_Campaign_TrackingSetting": {"description": "Campaign-level settings for tracking information.", "id": "GoogleAdsSearchads360V0Resources_Campaign_TrackingSetting", "properties": {"trackingUrl": {"description": "Output only. The url used for dynamic tracking.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ConversionAction_AttributionModelSettings": {"description": "Settings related to this conversion action's attribution model.", "id": "GoogleAdsSearchads360V0Resources_ConversionAction_AttributionModelSettings", "properties": {"attributionModel": {"description": "The attribution model type of this conversion action.", "enum": ["UNSPECIFIED", "UNKNOWN", "EXTERNAL", "GOOGLE_ADS_LAST_CLICK", "GOOGLE_SEARCH_ATTRIBUTION_FIRST_CLICK", "GOOGLE_SEARCH_ATTRIBUTION_LINEAR", "GOOGLE_SEARCH_ATTRIBUTION_TIME_DECAY", "GOOGLE_SEARCH_ATTRIBUTION_POSITION_BASED", "GOOGLE_SEARCH_ATTRIBUTION_DATA_DRIVEN"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Uses external attribution.", "Attributes all credit for a conversion to its last click.", "Attributes all credit for a conversion to its first click using Google Search attribution.", "Attributes credit for a conversion equally across all of its clicks using Google Search attribution.", "Attributes exponentially more credit for a conversion to its more recent clicks using Google Search attribution (half-life is 1 week).", "Attributes 40% of the credit for a conversion to its first and last clicks. Remaining 20% is evenly distributed across all other clicks. This uses Google Search attribution.", "Flexible model that uses machine learning to determine the appropriate distribution of credit among clicks using Google Search attribution."], "type": "string"}, "dataDrivenModelStatus": {"description": "Output only. The status of the data-driven attribution model for the conversion action.", "enum": ["UNSPECIFIED", "UNKNOWN", "AVAILABLE", "STALE", "EXPIRED", "NEVER_GENERATED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The data driven model is available.", "The data driven model is stale. It hasn't been updated for at least 7 days. It is still being used, but will become expired if it does not get updated for 30 days.", "The data driven model expired. It hasn't been updated for at least 30 days and cannot be used. Most commonly this is because there hasn't been the required number of events in a recent 30-day period.", "The data driven model has never been generated. Most commonly this is because there has never been the required number of events in any 30-day period."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ConversionAction_FloodlightSettings": {"description": "Settings related to a Floodlight conversion action.", "id": "GoogleAdsSearchads360V0Resources_ConversionAction_FloodlightSettings", "properties": {"activityGroupTag": {"description": "Output only. String used to identify a Floodlight activity group when reporting conversions.", "readOnly": true, "type": "string"}, "activityId": {"description": "Output only. ID of the Floodlight activity in DoubleClick Campaign Manager (DCM).", "format": "int64", "readOnly": true, "type": "string"}, "activityTag": {"description": "Output only. String used to identify a Floodlight activity when reporting conversions.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ConversionAction_ValueSettings": {"description": "Settings related to the value for conversion events associated with this conversion action.", "id": "GoogleAdsSearchads360V0Resources_ConversionAction_ValueSettings", "properties": {"alwaysUseDefaultValue": {"description": "Controls whether the default value and default currency code are used in place of the value and currency code specified in conversion events for this conversion action.", "type": "boolean"}, "defaultCurrencyCode": {"description": "The currency code to use when conversion events for this conversion action are sent with an invalid or missing currency code, or when this conversion action is configured to always use the default value.", "type": "string"}, "defaultValue": {"description": "The value to use when conversion events for this conversion action are sent with an invalid, disallowed or missing value, or when this conversion action is configured to always use the default value.", "format": "double", "type": "number"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductBiddingCategory": {"description": "One element of a bidding category at a certain level. Top-level categories are at level 1, their children at level 2, and so on. We currently support up to 5 levels. The user must specify a dimension type that indicates the level of the category. All cases of the same subdivision must have the same dimension type (category level).", "id": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductBiddingCategory", "properties": {"id": {"description": "ID of the product bidding category. This ID is equivalent to the google_product_category ID as described in this article: https://support.google.com/merchants/answer/6324436", "format": "int64", "type": "string"}, "level": {"description": "Indicates the level of the category in the taxonomy.", "enum": ["UNSPECIFIED", "UNKNOWN", "LEVEL1", "LEVEL2", "LEVEL3", "LEVEL4", "LEVEL5"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Level 1.", "Level 2.", "Level 3.", "Level 4.", "Level 5."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductBrand": {"description": "Brand of the product.", "id": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductBrand", "properties": {"value": {"description": "String value of the product brand.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductChannel": {"description": "Locality of a product offer.", "id": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductChannel", "properties": {"channel": {"description": "Value of the locality.", "enum": ["UNSPECIFIED", "UNKNOWN", "ONLINE", "LOCAL"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The item is sold online.", "The item is sold in local stores."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductCondition": {"description": "Condition of a product offer.", "id": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductCondition", "properties": {"condition": {"description": "Value of the condition.", "enum": ["UNSPECIFIED", "UNKNOWN", "NEW", "REFURBISHED", "USED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The product condition is new.", "The product condition is refurbished.", "The product condition is used."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductCustomAttribute": {"description": "Custom attribute of a product offer.", "id": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductCustomAttribute", "properties": {"index": {"description": "Indicates the index of the custom attribute.", "enum": ["UNSPECIFIED", "UNKNOWN", "INDEX0", "INDEX1", "INDEX2", "INDEX3", "INDEX4"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "First listing group filter custom attribute.", "Second listing group filter custom attribute.", "Third listing group filter custom attribute.", "Fourth listing group filter custom attribute.", "Fifth listing group filter custom attribute."], "type": "string"}, "value": {"description": "String value of the product custom attribute.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductItemId": {"description": "Item id of a product offer.", "id": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductItemId", "properties": {"value": {"description": "Value of the id.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductType": {"description": "Type of a product offer.", "id": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductType", "properties": {"level": {"description": "Level of the type.", "enum": ["UNSPECIFIED", "UNKNOWN", "LEVEL1", "LEVEL2", "LEVEL3", "LEVEL4", "LEVEL5"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Level 1.", "Level 2.", "Level 3.", "Level 4.", "Level 5."], "type": "string"}, "value": {"description": "Value of the type.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__Ad": {"description": "An ad.", "id": "GoogleAdsSearchads360V0Resources__Ad", "properties": {"displayUrl": {"description": "The URL that appears in the ad description for some ad formats.", "type": "string"}, "expandedDynamicSearchAd": {"$ref": "GoogleAdsSearchads360V0Common__SearchAds360ExpandedDynamicSearchAdInfo", "description": "Immutable. Details pertaining to an expanded dynamic search ad."}, "expandedTextAd": {"$ref": "GoogleAdsSearchads360V0Common__SearchAds360ExpandedTextAdInfo", "description": "Immutable. Details pertaining to an expanded text ad."}, "finalUrls": {"description": "The list of possible final URLs after all cross-domain redirects for the ad.", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Output only. The ID of the ad.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "Immutable. The name of the ad. This is only used to be able to identify the ad. It does not need to be unique and does not affect the served ad. The name field is currently only supported for DisplayUploadAd, ImageAd, ShoppingComparisonListingAd and VideoAd.", "type": "string"}, "productAd": {"$ref": "GoogleAdsSearchads360V0Common__SearchAds360ProductAdInfo", "description": "Immutable. Details pertaining to a product ad."}, "resourceName": {"description": "Immutable. The resource name of the ad. Ad resource names have the form: `customers/{customer_id}/ads/{ad_id}`", "type": "string"}, "responsiveSearchAd": {"$ref": "GoogleAdsSearchads360V0Common__SearchAds360ResponsiveSearchAdInfo", "description": "Immutable. Details pertaining to a responsive search ad."}, "textAd": {"$ref": "GoogleAdsSearchads360V0Common__SearchAds360TextAdInfo", "description": "Immutable. Details pertaining to a text ad."}, "type": {"description": "Output only. The type of ad.", "enum": ["UNSPECIFIED", "UNKNOWN", "TEXT_AD", "EXPANDED_TEXT_AD", "CALL_ONLY_AD", "EXPANDED_DYNAMIC_SEARCH_AD", "HOTEL_AD", "SHOPPING_SMART_AD", "SHOPPING_PRODUCT_AD", "VIDEO_AD", "GMAIL_AD", "IMAGE_AD", "RESPONSIVE_SEARCH_AD", "LEGACY_RESPONSIVE_DISPLAY_AD", "APP_AD", "LEGACY_APP_INSTALL_AD", "RESPONSIVE_DISPLAY_AD", "LOCAL_AD", "HTML5_UPLOAD_AD", "DYNAMIC_HTML5_AD", "APP_ENGAGEMENT_AD", "SHOPPING_COMPARISON_LISTING_AD", "VIDEO_BUMPER_AD", "VIDEO_NON_SKIPPABLE_IN_STREAM_AD", "VIDEO_OUTSTREAM_AD", "VIDEO_TRUEVIEW_DISCOVERY_AD", "VIDEO_TRUEVIEW_IN_STREAM_AD", "VIDEO_RESPONSIVE_AD", "SMART_CAMPAIGN_AD", "APP_PRE_REGISTRATION_AD", "DISCOVERY_MULTI_ASSET_AD", "DISCOVERY_CAROUSEL_AD", "TRAVEL_AD", "DISCOVERY_VIDEO_RESPONSIVE_AD"], "enumDescriptions": ["No value has been specified.", "The received value is not known in this version. This is a response-only value.", "The ad is a text ad.", "The ad is an expanded text ad.", "The ad is a call only ad.", "The ad is an expanded dynamic search ad.", "The ad is a hotel ad.", "The ad is a Smart Shopping ad.", "The ad is a standard Shopping ad.", "The ad is a video ad.", "This ad is a Gmail ad.", "This ad is an Image ad.", "The ad is a responsive search ad.", "The ad is a legacy responsive display ad.", "The ad is an app ad.", "The ad is a legacy app install ad.", "The ad is a responsive display ad.", "The ad is a local ad.", "The ad is a display upload ad with the HTML5_UPLOAD_AD product type.", "The ad is a display upload ad with one of the DYNAMIC_HTML5_* product types.", "The ad is an app engagement ad.", "The ad is a Shopping Comparison Listing ad.", "Video bumper ad.", "Video non-skippable in-stream ad.", "Video outstream ad.", "Video TrueView in-display ad.", "Video TrueView in-stream ad.", "Video responsive ad.", "Smart campaign ad.", "Universal app pre-registration ad.", "Discovery multi asset ad.", "Discovery carousel ad.", "Travel ad.", "Discovery video responsive ad."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroup": {"description": "An ad group.", "id": "GoogleAdsSearchads360V0Resources__AdGroup", "properties": {"adRotationMode": {"description": "The ad rotation mode of the ad group.", "enum": ["UNSPECIFIED", "UNKNOWN", "OPTIMIZE", "ROTATE_FOREVER"], "enumDescriptions": ["The ad rotation mode has not been specified.", "The received value is not known in this version. This is a response-only value.", "Optimize ad group ads based on clicks or conversions.", "Rotate evenly forever."], "type": "string"}, "cpcBidMicros": {"description": "The maximum CPC (cost-per-click) bid.", "format": "int64", "type": "string"}, "creationTime": {"description": "Output only. The timestamp when this ad_group was created. The timestamp is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss\" format.", "readOnly": true, "type": "string"}, "endDate": {"description": "Output only. Date when the ad group ends serving ads. By default, the ad group ends on the ad group's end date. If this field is set, then the ad group ends at the end of the specified date in the customer's time zone. This field is only available for Microsoft Advertising and Facebook gateway accounts. Format: YYYY-MM-DD Example: 2019-03-14", "readOnly": true, "type": "string"}, "engineId": {"description": "Output only. ID of the ad group in the external engine account. This field is for non-Google Ads account only, for example, Yahoo Japan, Microsoft, Baidu etc. For Google Ads entity, use \"ad_group.id\" instead.", "readOnly": true, "type": "string"}, "engineStatus": {"description": "Output only. The Engine Status for ad group.", "enum": ["UNSPECIFIED", "UNKNOWN", "AD_GROUP_ELIGIBLE", "AD_GROUP_EXPIRED", "AD_GROUP_REMOVED", "AD_GROUP_DRAFT", "AD_GROUP_PAUSED", "AD_GROUP_SERVING", "AD_GROUP_SUBMITTED", "CAMPAIGN_PAUSED", "ACCOUNT_PAUSED"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Deprecated. Do not use.", "No ads are running for this ad group, because the ad group's end date has passed.", "The ad group has been deleted.", "No ads are running for this ad group because the associated ad group is still in draft form.", "The ad group has been paused.", "The ad group is active and currently serving ads.", "The ad group has been submitted (Microsoft Bing Ads legacy status).", "No ads are running for this ad group, because the campaign has been paused.", "No ads are running for this ad group, because the account has been paused."], "readOnly": true, "type": "string"}, "id": {"description": "Output only. The ID of the ad group.", "format": "int64", "readOnly": true, "type": "string"}, "labels": {"description": "Output only. The resource names of labels attached to this ad group.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "languageCode": {"description": "Output only. The language of the ads and keywords in an ad group. This field is only available for Microsoft Advertising accounts. More details: https://docs.microsoft.com/en-us/advertising/guides/ad-languages?view=bingads-13#adlanguage", "readOnly": true, "type": "string"}, "lastModifiedTime": {"description": "Output only. The datetime when this ad group was last modified. The datetime is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss.ssssss\" format.", "readOnly": true, "type": "string"}, "name": {"description": "The name of the ad group. This field is required and should not be empty when creating new ad groups. It must contain fewer than 255 UTF-8 full-width characters. It must not contain any null (code point 0x0), NL line feed (code point 0xA) or carriage return (code point 0xD) characters.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the ad group. Ad group resource names have the form: `customers/{customer_id}/adGroups/{ad_group_id}`", "type": "string"}, "startDate": {"description": "Output only. Date when this ad group starts serving ads. By default, the ad group starts now or the ad group's start date, whichever is later. If this field is set, then the ad group starts at the beginning of the specified date in the customer's time zone. This field is only available for Microsoft Advertising and Facebook gateway accounts. Format: YYYY-MM-DD Example: 2019-03-14", "readOnly": true, "type": "string"}, "status": {"description": "The status of the ad group.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "PAUSED", "REMOVED"], "enumDescriptions": ["The status has not been specified.", "The received value is not known in this version. This is a response-only value.", "The ad group is enabled.", "The ad group is paused.", "The ad group is removed."], "type": "string"}, "targetingSetting": {"$ref": "GoogleAdsSearchads360V0Common__TargetingSetting", "description": "Setting for targeting related features."}, "type": {"description": "Immutable. The type of the ad group.", "enum": ["UNSPECIFIED", "UNKNOWN", "SEARCH_STANDARD", "DISPLAY_STANDARD", "SHOPPING_PRODUCT_ADS", "SHOPPING_SHOWCASE_ADS", "HOTEL_ADS", "SHOPPING_SMART_ADS", "VIDEO_BUMPER", "VIDEO_TRUE_VIEW_IN_STREAM", "VIDEO_TRUE_VIEW_IN_DISPLAY", "VIDEO_NON_SKIPPABLE_IN_STREAM", "VIDEO_OUTSTREAM", "SEARCH_DYNAMIC_ADS", "SHOPPING_COMPARISON_LISTING_ADS", "PROMOTED_HOTEL_ADS", "VIDEO_RESPONSIVE", "VIDEO_EFFICIENT_REACH", "SMART_CAMPAIGN_ADS", "TRAVEL_ADS"], "enumDescriptions": ["The type has not been specified.", "The received value is not known in this version. This is a response-only value.", "The default ad group type for Search campaigns.", "The default ad group type for Display campaigns.", "The ad group type for Shopping campaigns serving standard product ads.", "The type for ad groups that are limited to serving Showcase or Merchant ads in Shopping results.", "The default ad group type for Hotel campaigns.", "The type for ad groups in Smart Shopping campaigns.", "Short unskippable in-stream video ads.", "TrueView (skippable) in-stream video ads.", "TrueView in-display video ads.", "Unskippable in-stream video ads.", "Outstream video ads.", "Ad group type for Dynamic Search Ads ad groups.", "The type for ad groups in Shopping Comparison Listing campaigns.", "The ad group type for Promoted Hotel ad groups.", "Video responsive ad groups.", "Video efficient reach ad groups.", "Ad group type for Smart campaigns.", "Ad group type for Travel campaigns."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroupAd": {"description": "An ad group ad.", "id": "GoogleAdsSearchads360V0Resources__AdGroupAd", "properties": {"ad": {"$ref": "GoogleAdsSearchads360V0Resources__Ad", "description": "Immutable. The ad."}, "creationTime": {"description": "Output only. The timestamp when this ad_group_ad was created. The datetime is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss.ssssss\" format.", "readOnly": true, "type": "string"}, "engineId": {"description": "Output only. ID of the ad in the external engine account. This field is for SearchAds 360 account only, for example, Yahoo Japan, Microsoft, Baidu etc. For non-SearchAds 360 entity, use \"ad_group_ad.ad.id\" instead.", "readOnly": true, "type": "string"}, "engineStatus": {"description": "Output only. Additional status of the ad in the external engine account. Possible statuses (depending on the type of external account) include active, eligible, pending review, etc.", "enum": ["UNSPECIFIED", "UNKNOWN", "AD_GROUP_AD_ELIGIBLE", "AD_GROUP_AD_INAPPROPRIATE_FOR_CAMPAIGN", "AD_GROUP_AD_MOBILE_URL_UNDER_REVIEW", "AD_GROUP_AD_PARTIALLY_INVALID", "AD_GROUP_AD_TO_BE_ACTIVATED", "AD_GROUP_AD_NOT_REVIEWED", "AD_GROUP_AD_ON_HOLD", "AD_GROUP_AD_PAUSED", "AD_GROUP_AD_REMOVED", "AD_GROUP_AD_PENDING_REVIEW", "AD_GROUP_AD_UNDER_REVIEW", "AD_GROUP_AD_APPROVED", "AD_GROUP_AD_DISAPPROVED", "AD_GROUP_AD_SERVING", "AD_GROUP_AD_ACCOUNT_PAUSED", "AD_GROUP_AD_CAMPAIGN_PAUSED", "AD_GROUP_AD_AD_GROUP_PAUSED"], "enumDeprecated": [false, false, true, false, false, false, false, false, true, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["No value has been specified.", "Used for return value only. Represents value unknown in this version.", "Deprecated. Do not use.", "Baidu: Creative was not approved.", "Baidu: Mobile URL in process to be reviewed.", "Baidu: Creative is invalid on mobile device but valid on desktop.", "Baidu: Creative is ready for activation.", "Baidu: Creative not reviewed.", "Deprecated. Do not use. Previously used by Gemini", "Creative has been paused.", "Creative has been removed.", "Creative is pending review.", "Creative is under review.", "Creative has been approved.", "Creative has been disapproved.", "Creative is serving.", "Creative has been paused because the account is paused.", "Creative has been paused because the campaign is paused.", "Creative has been paused because the ad group is paused."], "readOnly": true, "type": "string"}, "labels": {"description": "Output only. The resource names of labels attached to this ad group ad.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "lastModifiedTime": {"description": "Output only. The datetime when this ad group ad was last modified. The datetime is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss.ssssss\" format.", "readOnly": true, "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the ad. Ad group ad resource names have the form: `customers/{customer_id}/adGroupAds/{ad_group_id}~{ad_id}`", "type": "string"}, "status": {"description": "The status of the ad.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "PAUSED", "REMOVED"], "enumDescriptions": ["No value has been specified.", "The received value is not known in this version. This is a response-only value.", "The ad group ad is enabled.", "The ad group ad is paused.", "The ad group ad is removed."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroupAdLabel": {"description": "A relationship between an ad group ad and a label.", "id": "GoogleAdsSearchads360V0Resources__AdGroupAdLabel", "properties": {"adGroupAd": {"description": "Immutable. The ad group ad to which the label is attached.", "type": "string"}, "label": {"description": "Immutable. The label assigned to the ad group ad.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the ad group ad label. Ad group ad label resource names have the form: `customers/{customer_id}/adGroupAdLabels/{ad_group_id}~{ad_id}~{label_id}`", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroupAsset": {"description": "A link between an ad group and an asset.", "id": "GoogleAdsSearchads360V0Resources__AdGroupAsset", "properties": {"adGroup": {"description": "Required. Immutable. The ad group to which the asset is linked.", "type": "string"}, "asset": {"description": "Required. Immutable. The asset which is linked to the ad group.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the ad group asset. AdGroupAsset resource names have the form: `customers/{customer_id}/adGroupAssets/{ad_group_id}~{asset_id}~{field_type}`", "type": "string"}, "status": {"description": "Status of the ad group asset.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED", "PAUSED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Asset link is enabled.", "Asset link has been removed.", "Asset link is paused."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroupAssetSet": {"description": "AdGroupAssetSet is the linkage between an ad group and an asset set. Creating an AdGroupAssetSet links an asset set with an ad group.", "id": "GoogleAdsSearchads360V0Resources__AdGroupAssetSet", "properties": {"adGroup": {"description": "Immutable. The ad group to which this asset set is linked.", "type": "string"}, "assetSet": {"description": "Immutable. The asset set which is linked to the ad group.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the ad group asset set. Ad group asset set resource names have the form: `customers/{customer_id}/adGroupAssetSets/{ad_group_id}~{asset_set_id}`", "type": "string"}, "status": {"description": "Output only. The status of the ad group asset set. Read-only.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED"], "enumDescriptions": ["The status has not been specified.", "The received value is not known in this version. This is a response-only value.", "The linkage between asset set and its container is enabled.", "The linkage between asset set and its container is removed."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroupAudienceView": {"description": "An ad group audience view. Includes performance data from interests and remarketing lists for Display Network and YouTube Network ads, and remarketing lists for search ads (RLSA), aggregated at the audience level.", "id": "GoogleAdsSearchads360V0Resources__AdGroupAudienceView", "properties": {"resourceName": {"description": "Output only. The resource name of the ad group audience view. Ad group audience view resource names have the form: `customers/{customer_id}/adGroupAudienceViews/{ad_group_id}~{criterion_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroupBidModifier": {"description": "Represents an ad group bid modifier.", "id": "GoogleAdsSearchads360V0Resources__AdGroupBidModifier", "properties": {"bidModifier": {"description": "The modifier for the bid when the criterion matches. The modifier must be in the range: 0.1 - 10.0. The range is 1.0 - 6.0 for PreferredContent. Use 0 to opt out of a Device type.", "format": "double", "type": "number"}, "device": {"$ref": "GoogleAdsSearchads360V0Common__DeviceInfo", "description": "Immutable. A device criterion."}, "resourceName": {"description": "Immutable. The resource name of the ad group bid modifier. Ad group bid modifier resource names have the form: `customers/{customer_id}/adGroupBidModifiers/{ad_group_id}~{criterion_id}`", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroupCriterion": {"description": "An ad group criterion.", "id": "GoogleAdsSearchads360V0Resources__AdGroupCriterion", "properties": {"adGroup": {"description": "Immutable. The ad group to which the criterion belongs.", "type": "string"}, "ageRange": {"$ref": "GoogleAdsSearchads360V0Common__AgeRangeInfo", "description": "Immutable. Age range."}, "bidModifier": {"description": "The modifier for the bid when the criterion matches. The modifier must be in the range: 0.1 - 10.0. Most targetable criteria types support modifiers.", "format": "double", "type": "number"}, "cpcBidMicros": {"description": "The CPC (cost-per-click) bid.", "format": "int64", "type": "string"}, "creationTime": {"description": "Output only. The timestamp when this ad group criterion was created. The timestamp is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss\" format.", "readOnly": true, "type": "string"}, "criterionId": {"description": "Output only. The ID of the criterion.", "format": "int64", "readOnly": true, "type": "string"}, "effectiveCpcBidMicros": {"description": "Output only. The effective CPC (cost-per-click) bid.", "format": "int64", "readOnly": true, "type": "string"}, "engineId": {"description": "Output only. ID of the ad group criterion in the external engine account. This field is for non-Google Ads account only, for example, Yahoo Japan, Microsoft, Baidu etc. For Google Ads entity, use \"ad_group_criterion.criterion_id\" instead.", "readOnly": true, "type": "string"}, "engineStatus": {"description": "Output only. The Engine Status for ad group criterion.", "enum": ["UNSPECIFIED", "UNKNOWN", "AD_GROUP_CRITERION_ELIGIBLE", "AD_GROUP_CRITERION_INAPPROPRIATE_FOR_CAMPAIGN", "AD_GROUP_CRITERION_INVALID_MOBILE_SEARCH", "AD_GROUP_CRITERION_INVALID_PC_SEARCH", "AD_GROUP_CRITERION_INVALID_SEARCH", "AD_GROUP_CRITERION_LOW_SEARCH_VOLUME", "AD_GROUP_CRITERION_MOBILE_URL_UNDER_REVIEW", "AD_GROUP_CRITERION_PARTIALLY_INVALID", "AD_GROUP_CRITERION_TO_BE_ACTIVATED", "AD_GROUP_CRITERION_UNDER_REVIEW", "AD_GROUP_CRITERION_NOT_REVIEWED", "AD_GROUP_CRITERION_ON_HOLD", "AD_GROUP_CRITERION_PENDING_REVIEW", "AD_GROUP_CRITERION_PAUSED", "AD_GROUP_CRITERION_REMOVED", "AD_GROUP_CRITERION_APPROVED", "AD_GROUP_CRITERION_DISAPPROVED", "AD_GROUP_CRITERION_SERVING", "AD_GROUP_CRITERION_ACCOUNT_PAUSED"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, true, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Deprecated. Do not use.", "Baidu: Bid or quality too low to be displayed.", "Baidu: Bid or quality too low for mobile, but eligible to display for desktop.", "Baidu: Bid or quality too low for desktop, but eligible to display for mobile.", "Baidu: Bid or quality too low to be displayed.", "Baidu: Paused by Bai<PERSON> due to low search volume.", "Baidu: Mobile URL in process to be reviewed.", "Baidu: The landing page for one device is invalid, while the landing page for the other device is valid.", "Baidu: Keyword has been created and paused by Baidu account management, and is now ready for you to activate it.", "Baidu: In process to be reviewed by Baidu. Gemini: Criterion under review.", "Baidu: Criterion to be reviewed.", "Deprecated. Do not use. Previously used by Gemini", "Y!J : Criterion pending review", "Criterion has been paused.", "Criterion has been removed.", "Criterion has been approved.", "Criterion has been disapproved.", "Criterion is active and serving.", "Criterion has been paused since the account is paused."], "readOnly": true, "type": "string"}, "finalUrlSuffix": {"description": "URL template for appending params to final URL.", "type": "string"}, "finalUrls": {"description": "The list of possible final URLs after all cross-domain redirects for the ad.", "items": {"type": "string"}, "type": "array"}, "gender": {"$ref": "GoogleAdsSearchads360V0Common__GenderInfo", "description": "Immutable. Gender."}, "keyword": {"$ref": "GoogleAdsSearchads360V0Common__KeywordInfo", "description": "Immutable. Keyword."}, "labels": {"description": "Output only. The resource names of labels attached to this ad group criterion.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "lastModifiedTime": {"description": "Output only. The datetime when this ad group criterion was last modified. The datetime is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss.ssssss\" format.", "readOnly": true, "type": "string"}, "listingGroup": {"$ref": "GoogleAdsSearchads360V0Common__ListingGroupInfo", "description": "Immutable. Listing group."}, "location": {"$ref": "GoogleAdsSearchads360V0Common__LocationInfo", "description": "Immutable. Location."}, "negative": {"description": "Immutable. Whether to target (`false`) or exclude (`true`) the criterion. This field is immutable. To switch a criterion from positive to negative, remove then re-add it.", "type": "boolean"}, "positionEstimates": {"$ref": "GoogleAdsSearchads360V0Resources_AdGroupCriterion_PositionEstimates", "description": "Output only. Estimates for criterion bids at various positions.", "readOnly": true}, "qualityInfo": {"$ref": "GoogleAdsSearchads360V0Resources_AdGroupCriterion_QualityInfo", "description": "Output only. Information regarding the quality of the criterion.", "readOnly": true}, "resourceName": {"description": "Immutable. The resource name of the ad group criterion. Ad group criterion resource names have the form: `customers/{customer_id}/adGroupCriteria/{ad_group_id}~{criterion_id}`", "type": "string"}, "status": {"description": "The status of the criterion. This is the status of the ad group criterion entity, set by the client. Note: UI reports may incorporate additional information that affects whether a criterion is eligible to run. In some cases a criterion that's REMOVED in the API can still show as enabled in the UI. For example, campaigns by default show to users of all age ranges unless excluded. The UI will show each age range as \"enabled\", since they're eligible to see the ads; but AdGroupCriterion.status will show \"removed\", since no positive criterion was added.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "PAUSED", "REMOVED"], "enumDescriptions": ["No value has been specified.", "The received value is not known in this version. This is a response-only value.", "The ad group criterion is enabled.", "The ad group criterion is paused.", "The ad group criterion is removed."], "type": "string"}, "trackingUrlTemplate": {"description": "The URL template for constructing a tracking URL.", "type": "string"}, "type": {"description": "Output only. The type of the criterion.", "enum": ["UNSPECIFIED", "UNKNOWN", "KEYWORD", "PLACEMENT", "MOBILE_APP_CATEGORY", "MOBILE_APPLICATION", "DEVICE", "LOCATION", "LISTING_GROUP", "AD_SCHEDULE", "AGE_RANGE", "GENDER", "INCOME_RANGE", "PARENTAL_STATUS", "YOUTUBE_VIDEO", "YOUTUBE_CHANNEL", "USER_LIST", "PROXIMITY", "TOPIC", "LISTING_SCOPE", "LANGUAGE", "IP_BLOCK", "CONTENT_LABEL", "CARRIER", "USER_INTEREST", "WEBPAGE", "OPERATING_SYSTEM_VERSION", "APP_PAYMENT_MODEL", "MOBILE_DEVICE", "CUSTOM_AFFINITY", "CUSTOM_INTENT", "LOCATION_GROUP", "CUSTOM_AUDIENCE", "COMBINED_AUDIENCE", "KEYWORD_THEME", "AUDIENCE", "LOCAL_SERVICE_ID", "BRAND", "BRAND_LIST"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Keyword, for example, 'mars cruise'.", "Placement, also known as Website, for example, 'www.flowers4sale.com'", "Mobile application categories to target.", "Mobile applications to target.", "Devices to target.", "Locations to target.", "Listing groups to target.", "Ad Schedule.", "Age range.", "Gender.", "Income Range.", "Parental status.", "YouTube Video.", "YouTube Channel.", "User list.", "Proximity.", "A topic target on the display network (for example, \"Pets & Animals\").", "Listing scope to target.", "Language.", "<PERSON><PERSON><PERSON><PERSON>.", "Content Label for category exclusion.", "Carrier.", "A category the user is interested in.", "Webpage criterion for dynamic search ads.", "Operating system version.", "App payment model.", "Mobile device.", "Custom affinity.", "Custom intent.", "Location group.", "Custom audience", "Combined audience", "Smart Campaign keyword theme", "Audience", "Local Services Ads Service ID.", "Brand", "Brand List"], "readOnly": true, "type": "string"}, "userList": {"$ref": "GoogleAdsSearchads360V0Common__UserListInfo", "description": "Immutable. User List. The Similar Audiences sunset starts May 2023. Refer to https://ads-developers.googleblog.com/2022/11/announcing-deprecation-and-sunset-of.html for other options."}, "webpage": {"$ref": "GoogleAdsSearchads360V0Common__WebpageInfo", "description": "Immutable. Webpage"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroupCriterionLabel": {"description": "A relationship between an ad group criterion and a label.", "id": "GoogleAdsSearchads360V0Resources__AdGroupCriterionLabel", "properties": {"adGroupCriterion": {"description": "Immutable. The ad group criterion to which the label is attached.", "type": "string"}, "label": {"description": "Immutable. The label assigned to the ad group criterion.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the ad group criterion label. Ad group criterion label resource names have the form: `customers/{customer_id}/adGroupCriterionLabels/{ad_group_id}~{criterion_id}~{label_id}`", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AdGroupLabel": {"description": "A relationship between an ad group and a label.", "id": "GoogleAdsSearchads360V0Resources__AdGroupLabel", "properties": {"adGroup": {"description": "Immutable. The ad group to which the label is attached.", "type": "string"}, "label": {"description": "Immutable. The label assigned to the ad group.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the ad group label. Ad group label resource names have the form: `customers/{customer_id}/adGroupLabels/{ad_group_id}~{label_id}`", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AgeRangeView": {"description": "An age range view.", "id": "GoogleAdsSearchads360V0Resources__AgeRangeView", "properties": {"resourceName": {"description": "Output only. The resource name of the age range view. Age range view resource names have the form: `customers/{customer_id}/ageRangeViews/{ad_group_id}~{criterion_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__Asset": {"description": "Asset is a part of an ad which can be shared across multiple ads. It can be an image (ImageAsset), a video (YoutubeVideoAsset), etc. Assets are immutable and cannot be removed. To stop an asset from serving, remove the asset from the entity that is using it.", "id": "GoogleAdsSearchads360V0Resources__Asset", "properties": {"callAsset": {"$ref": "GoogleAdsSearchads360V0Common__UnifiedCallAsset", "description": "Output only. A unified call asset.", "readOnly": true}, "callToActionAsset": {"$ref": "GoogleAdsSearchads360V0Common__CallToActionAsset", "description": "Immutable. A call to action asset."}, "calloutAsset": {"$ref": "GoogleAdsSearchads360V0Common__UnifiedCalloutAsset", "description": "Output only. A unified callout asset.", "readOnly": true}, "creationTime": {"description": "Output only. The timestamp when this asset was created. The timestamp is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss\" format.", "readOnly": true, "type": "string"}, "engineStatus": {"description": "Output only. The Engine Status for an asset.", "enum": ["UNSPECIFIED", "UNKNOWN", "SERVING", "SERVING_LIMITED", "DISAPPROVED", "DISABLED", "REMOVED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The asset is active.", "The asset is active limited.", "The asset is disapproved (not eligible).", "The asset is inactive (pending).", "The asset has been removed."], "readOnly": true, "type": "string"}, "finalUrls": {"description": "A list of possible final URLs after all cross domain redirects.", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Output only. The ID of the asset.", "format": "int64", "readOnly": true, "type": "string"}, "imageAsset": {"$ref": "GoogleAdsSearchads360V0Common__ImageAsset", "description": "Output only. An image asset.", "readOnly": true}, "lastModifiedTime": {"description": "Output only. The datetime when this asset was last modified. The datetime is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss.ssssss\" format.", "readOnly": true, "type": "string"}, "locationAsset": {"$ref": "GoogleAdsSearchads360V0Common__UnifiedLocationAsset", "description": "Output only. A unified location asset.", "readOnly": true}, "mobileAppAsset": {"$ref": "GoogleAdsSearchads360V0Common__MobileAppAsset", "description": "A mobile app asset."}, "name": {"description": "Optional name of the asset.", "type": "string"}, "pageFeedAsset": {"$ref": "GoogleAdsSearchads360V0Common__UnifiedPageFeedAsset", "description": "Output only. A unified page feed asset.", "readOnly": true}, "resourceName": {"description": "Immutable. The resource name of the asset. Asset resource names have the form: `customers/{customer_id}/assets/{asset_id}`", "type": "string"}, "sitelinkAsset": {"$ref": "GoogleAdsSearchads360V0Common__UnifiedSitelinkAsset", "description": "Output only. A unified sitelink asset.", "readOnly": true}, "status": {"description": "Output only. The status of the asset.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED", "ARCHIVED", "PENDING_SYSTEM_GENERATED"], "enumDescriptions": ["The status has not been specified.", "The received value is not known in this version. This is a response-only value.", "The asset is enabled.", "The asset is removed.", "The asset is archived.", "The asset is system generated pending user review."], "readOnly": true, "type": "string"}, "textAsset": {"$ref": "GoogleAdsSearchads360V0Common__TextAsset", "description": "Output only. A text asset.", "readOnly": true}, "trackingUrlTemplate": {"description": "URL template for constructing a tracking URL.", "type": "string"}, "type": {"description": "Output only. Type of the asset.", "enum": ["UNSPECIFIED", "UNKNOWN", "YOUTUBE_VIDEO", "MEDIA_BUNDLE", "IMAGE", "TEXT", "LEAD_FORM", "BOOK_ON_GOOGLE", "PROMOTION", "CALLOUT", "STRUCTURED_SNIPPET", "SITELINK", "PAGE_FEED", "DYNAMIC_EDUCATION", "MOBILE_APP", "HOTEL_CALLOUT", "CALL", "PRICE", "CALL_TO_ACTION", "DYNAMIC_REAL_ESTATE", "DYNAMIC_CUSTOM", "DYNAMIC_HOTELS_AND_RENTALS", "DYNAMIC_FLIGHTS", "DISCOVERY_CAROUSEL_CARD", "DYNAMIC_TRAVEL", "DYNAMIC_LOCAL", "DYNAMIC_JOBS", "LOCATION", "HOTEL_PROPERTY"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "YouTube video asset.", "Media bundle asset.", "Image asset.", "Text asset.", "Lead form asset.", "Book on Google asset.", "Promotion asset.", "Callout asset.", "Structured Snippet asset.", "Sitelink asset.", "Page Feed asset.", "Dynamic Education asset.", "Mobile app asset.", "Hotel callout asset.", "Call asset.", "Price asset.", "Call to action asset.", "Dynamic real estate asset.", "Dynamic custom asset.", "Dynamic hotels and rentals asset.", "Dynamic flights asset.", "Discovery Carousel Card asset.", "Dynamic travel asset.", "Dynamic local asset.", "Dynamic jobs asset.", "Location asset.", "Hotel property asset."], "readOnly": true, "type": "string"}, "youtubeVideoAsset": {"$ref": "GoogleAdsSearchads360V0Common__YoutubeVideoAsset", "description": "Immutable. A YouTube video asset."}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AssetGroup": {"description": "An asset group. AssetGroupAsset is used to link an asset to the asset group. AssetGroupSignal is used to associate a signal to an asset group.", "id": "GoogleAdsSearchads360V0Resources__AssetGroup", "properties": {"adStrength": {"description": "Output only. Overall ad strength of this asset group.", "enum": ["UNSPECIFIED", "UNKNOWN", "PENDING", "NO_ADS", "POOR", "AVERAGE", "GOOD", "EXCELLENT"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The ad strength is currently pending.", "No ads could be generated.", "Poor strength.", "Average strength.", "Good strength.", "Excellent strength."], "readOnly": true, "type": "string"}, "campaign": {"description": "Immutable. The campaign with which this asset group is associated. The asset which is linked to the asset group.", "type": "string"}, "finalMobileUrls": {"description": "A list of final mobile URLs after all cross domain redirects. In performance max, by default, the urls are eligible for expansion unless opted out.", "items": {"type": "string"}, "type": "array"}, "finalUrls": {"description": "A list of final URLs after all cross domain redirects. In performance max, by default, the urls are eligible for expansion unless opted out.", "items": {"type": "string"}, "type": "array"}, "id": {"description": "Output only. The ID of the asset group.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "Required. Name of the asset group. Required. It must have a minimum length of 1 and maximum length of 128. It must be unique under a campaign.", "type": "string"}, "path1": {"description": "First part of text that may appear appended to the url displayed in the ad.", "type": "string"}, "path2": {"description": "Second part of text that may appear appended to the url displayed in the ad. This field can only be set when path1 is set.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the asset group. Asset group resource names have the form: `customers/{customer_id}/assetGroups/{asset_group_id}`", "type": "string"}, "status": {"description": "The status of the asset group.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "PAUSED", "REMOVED"], "enumDescriptions": ["The status has not been specified.", "The received value is not known in this version.", "The asset group is enabled.", "The asset group is paused.", "The asset group is removed."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AssetGroupAsset": {"description": "AssetGroupAsset is the link between an asset and an asset group. Adding an AssetGroupAsset links an asset with an asset group.", "id": "GoogleAdsSearchads360V0Resources__AssetGroupAsset", "properties": {"asset": {"description": "Immutable. The asset which this asset group asset is linking.", "type": "string"}, "assetGroup": {"description": "Immutable. The asset group which this asset group asset is linking.", "type": "string"}, "fieldType": {"description": "The description of the placement of the asset within the asset group. For example: HEADLINE, YOUTUBE_VIDEO etc", "enum": ["UNSPECIFIED", "UNKNOWN", "HEADLINE", "DESCRIPTION", "MANDATORY_AD_TEXT", "MARKETING_IMAGE", "MEDIA_BUNDLE", "YOUTUBE_VIDEO", "BOOK_ON_GOOGLE", "LEAD_FORM", "PROMOTION", "CALLOUT", "STRUCTURED_SNIPPET", "SITELINK", "MOBILE_APP", "HOTEL_CALLOUT", "CALL", "PRICE", "LONG_HEADLINE", "BUSINESS_NAME", "SQUARE_MARKETING_IMAGE", "PORTRAIT_MARKETING_IMAGE", "LOGO", "LANDSCAPE_LOGO", "VIDEO", "CALL_TO_ACTION_SELECTION", "AD_IMAGE", "BUSINESS_LOGO", "HOTEL_PROPERTY"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The asset is linked for use as a headline.", "The asset is linked for use as a description.", "The asset is linked for use as mandatory ad text.", "The asset is linked for use as a marketing image.", "The asset is linked for use as a media bundle.", "The asset is linked for use as a YouTube video.", "The asset is linked to indicate that a hotels campaign is \"Book on Google\" enabled.", "The asset is linked for use as a Lead Form extension.", "The asset is linked for use as a Promotion extension.", "The asset is linked for use as a Callout extension.", "The asset is linked for use as a Structured Snippet extension.", "The asset is linked for use as a Sitelink.", "The asset is linked for use as a Mobile App extension.", "The asset is linked for use as a Hotel Callout extension.", "The asset is linked for use as a Call extension.", "The asset is linked for use as a Price extension.", "The asset is linked for use as a long headline.", "The asset is linked for use as a business name.", "The asset is linked for use as a square marketing image.", "The asset is linked for use as a portrait marketing image.", "The asset is linked for use as a logo.", "The asset is linked for use as a landscape logo.", "The asset is linked for use as a non YouTube logo.", "The asset is linked for use to select a call-to-action.", "The asset is linked for use to select an ad image.", "The asset is linked for use as a business logo.", "The asset is linked for use as a hotel property in a Performance Max for travel goals campaign."], "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the asset group asset. Asset group asset resource name have the form: `customers/{customer_id}/assetGroupAssets/{asset_group_id}~{asset_id}~{field_type}`", "type": "string"}, "status": {"description": "The status of the link between an asset and asset group.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED", "PAUSED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Asset link is enabled.", "Asset link has been removed.", "Asset link is paused."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AssetGroupAssetCombinationData": {"description": "Asset group asset combination data", "id": "GoogleAdsSearchads360V0Resources__AssetGroupAssetCombinationData", "properties": {"assetCombinationServedAssets": {"description": "Output only. Served assets.", "items": {"$ref": "GoogleAdsSearchads360V0Common__AssetUsage"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AssetGroupListingGroupFilter": {"description": "AssetGroupListingGroupFilter represents a listing group filter tree node in an asset group.", "id": "GoogleAdsSearchads360V0Resources__AssetGroupListingGroupFilter", "properties": {"assetGroup": {"description": "Immutable. The asset group which this asset group listing group filter is part of.", "type": "string"}, "caseValue": {"$ref": "GoogleAdsSearchads360V0Resources__ListingGroupFilterDimension", "description": "Dimension value with which this listing group is refining its parent. Undefined for the root group."}, "id": {"description": "Output only. The ID of the ListingGroupFilter.", "format": "int64", "readOnly": true, "type": "string"}, "parentListingGroupFilter": {"description": "Immutable. Resource name of the parent listing group subdivision. Null for the root listing group filter node.", "type": "string"}, "path": {"$ref": "GoogleAdsSearchads360V0Resources__ListingGroupFilterDimensionPath", "description": "Output only. The path of dimensions defining this listing group filter.", "readOnly": true}, "resourceName": {"description": "Immutable. The resource name of the asset group listing group filter. Asset group listing group filter resource name have the form: `customers/{customer_id}/assetGroupListingGroupFilters/{asset_group_id}~{listing_group_filter_id}`", "type": "string"}, "type": {"description": "Immutable. Type of a listing group filter node.", "enum": ["UNSPECIFIED", "UNKNOWN", "SUBDIVISION", "UNIT_INCLUDED", "UNIT_EXCLUDED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Subdivision of products along some listing dimensions.", "An included listing group filter leaf node.", "An excluded listing group filter leaf node."], "type": "string"}, "vertical": {"description": "Immutable. The vertical the current node tree represents. All nodes in the same tree must belong to the same vertical.", "enum": ["UNSPECIFIED", "UNKNOWN", "SHOPPING"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Represents the shopping vertical."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AssetGroupSignal": {"description": "AssetGroupSignal represents a signal in an asset group. The existence of a signal tells the performance max campaign who's most likely to convert. Performance Max uses the signal to look for new people with similar or stronger intent to find conversions across Search, Display, Video, and more.", "id": "GoogleAdsSearchads360V0Resources__AssetGroupSignal", "properties": {"assetGroup": {"description": "Immutable. The asset group which this asset group signal belongs to.", "type": "string"}, "audience": {"$ref": "GoogleAdsSearchads360V0Common__AudienceInfo", "description": "Immutable. The audience signal to be used by the performance max campaign."}, "resourceName": {"description": "Immutable. The resource name of the asset group signal. Asset group signal resource name have the form: `customers/{customer_id}/assetGroupSignals/{asset_group_id}~{signal_id}`", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AssetGroupTopCombinationView": {"description": "A view on the usage of ad group ad asset combination.", "id": "GoogleAdsSearchads360V0Resources__AssetGroupTopCombinationView", "properties": {"assetGroupTopCombinations": {"description": "Output only. The top combinations of assets that served together.", "items": {"$ref": "GoogleAdsSearchads360V0Resources__AssetGroupAssetCombinationData"}, "readOnly": true, "type": "array"}, "resourceName": {"description": "Output only. The resource name of the asset group top combination view. AssetGroup Top Combination view resource names have the form: `\"customers/{customer_id}/assetGroupTopCombinationViews/{asset_group_id}~{asset_combination_category}\"", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AssetSet": {"description": "An asset set representing a collection of assets. Use AssetSetAsset to link an asset to the asset set.", "id": "GoogleAdsSearchads360V0Resources__AssetSet", "properties": {"id": {"description": "Output only. The ID of the asset set.", "format": "int64", "readOnly": true, "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the asset set. Asset set resource names have the form: `customers/{customer_id}/assetSets/{asset_set_id}`", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__AssetSetAsset": {"description": "AssetSetAsset is the link between an asset and an asset set. Adding an AssetSetAsset links an asset with an asset set.", "id": "GoogleAdsSearchads360V0Resources__AssetSetAsset", "properties": {"asset": {"description": "Immutable. The asset which this asset set asset is linking to.", "type": "string"}, "assetSet": {"description": "Immutable. The asset set which this asset set asset is linking to.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the asset set asset. Asset set asset resource names have the form: `customers/{customer_id}/assetSetAssets/{asset_set_id}~{asset_id}`", "type": "string"}, "status": {"description": "Output only. The status of the asset set asset. Read-only.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED"], "enumDescriptions": ["The status has not been specified.", "The received value is not known in this version. This is a response-only value.", "The asset set asset is enabled.", "The asset set asset is removed."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__Audience": {"description": "Audience is an effective targeting option that lets you intersect different segment attributes, such as detailed demographics and affinities, to create audiences that represent sections of your target segments.", "id": "GoogleAdsSearchads360V0Resources__Audience", "properties": {"description": {"description": "Description of this audience.", "type": "string"}, "id": {"description": "Output only. ID of the audience.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "Required. Name of the audience. It should be unique across all audiences. It must have a minimum length of 1 and maximum length of 255.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the audience. Audience names have the form: `customers/{customer_id}/audiences/{audience_id}`", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__BiddingStrategy": {"description": "A bidding strategy.", "id": "GoogleAdsSearchads360V0Resources__BiddingStrategy", "properties": {"campaignCount": {"description": "Output only. The number of campaigns attached to this bidding strategy. This field is read-only.", "format": "int64", "readOnly": true, "type": "string"}, "currencyCode": {"description": "Immutable. The currency used by the bidding strategy (ISO 4217 three-letter code). For bidding strategies in manager customers, this currency can be set on creation and defaults to the manager customer's currency. For serving customers, this field cannot be set; all strategies in a serving customer implicitly use the serving customer's currency. In all cases the effective_currency_code field returns the currency used by the strategy.", "type": "string"}, "effectiveCurrencyCode": {"description": "Output only. The currency used by the bidding strategy (ISO 4217 three-letter code). For bidding strategies in manager customers, this is the currency set by the advertiser when creating the strategy. For serving customers, this is the customer's currency_code. Bidding strategy metrics are reported in this currency. This field is read-only.", "readOnly": true, "type": "string"}, "enhancedCpc": {"$ref": "GoogleAdsSearchads360V0Common__EnhancedCpc", "description": "A bidding strategy that raises bids for clicks that seem more likely to lead to a conversion and lowers them for clicks where they seem less likely."}, "id": {"description": "Output only. The ID of the bidding strategy.", "format": "int64", "readOnly": true, "type": "string"}, "maximizeConversionValue": {"$ref": "GoogleAdsSearchads360V0Common__MaximizeConversionValue", "description": "An automated bidding strategy to help get the most conversion value for your campaigns while spending your budget."}, "maximizeConversions": {"$ref": "GoogleAdsSearchads360V0Common__MaximizeConversions", "description": "An automated bidding strategy to help get the most conversions for your campaigns while spending your budget."}, "name": {"description": "The name of the bidding strategy. All bidding strategies within an account must be named distinctly. The length of this string should be between 1 and 255, inclusive, in UTF-8 bytes, (trimmed).", "type": "string"}, "nonRemovedCampaignCount": {"description": "Output only. The number of non-removed campaigns attached to this bidding strategy. This field is read-only.", "format": "int64", "readOnly": true, "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the bidding strategy. Bidding strategy resource names have the form: `customers/{customer_id}/biddingStrategies/{bidding_strategy_id}`", "type": "string"}, "status": {"description": "Output only. The status of the bidding strategy. This field is read-only.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED"], "enumDescriptions": ["No value has been specified.", "The received value is not known in this version. This is a response-only value.", "The bidding strategy is enabled.", "The bidding strategy is removed."], "readOnly": true, "type": "string"}, "targetCpa": {"$ref": "GoogleAdsSearchads360V0Common__TargetCpa", "description": "A bidding strategy that sets bids to help get as many conversions as possible at the target cost-per-acquisition (CPA) you set."}, "targetImpressionShare": {"$ref": "GoogleAdsSearchads360V0Common__TargetImpressionShare", "description": "A bidding strategy that automatically optimizes towards a chosen percentage of impressions."}, "targetOutrankShare": {"$ref": "GoogleAdsSearchads360V0Common__TargetOutrankShare", "description": "A bidding strategy that sets bids based on the target fraction of auctions where the advertiser should outrank a specific competitor. This field is deprecated. Creating a new bidding strategy with this field or attaching bidding strategies with this field to a campaign will fail. Mutates to strategies that already have this scheme populated are allowed."}, "targetRoas": {"$ref": "GoogleAdsSearchads360V0Common__TargetRoas", "description": "A bidding strategy that helps you maximize revenue while averaging a specific target Return On Ad Spend (ROAS)."}, "targetSpend": {"$ref": "GoogleAdsSearchads360V0Common__TargetSpend", "description": "A bid strategy that sets your bids to help get as many clicks as possible within your budget."}, "type": {"description": "Output only. The type of the bidding strategy. Create a bidding strategy by setting the bidding scheme. This field is read-only.", "enum": ["UNSPECIFIED", "UNKNOWN", "COMMISSION", "ENHANCED_CPC", "INVALID", "MANUAL_CPA", "MANUAL_CPC", "MANUAL_CPM", "MANUAL_CPV", "MAXIMIZE_CONVERSIONS", "MAXIMIZE_CONVERSION_VALUE", "PAGE_ONE_PROMOTED", "PERCENT_CPC", "TARGET_CPA", "TARGET_CPM", "TARGET_IMPRESSION_SHARE", "TARGET_OUTRANK_SHARE", "TARGET_ROAS", "TARGET_SPEND"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Commission is an automatic bidding strategy in which the advertiser pays a certain portion of the conversion value.", "Enhanced CPC is a bidding strategy that raises bids for clicks that seem more likely to lead to a conversion and lowers them for clicks where they seem less likely.", "Used for return value only. Indicates that a campaign does not have a bidding strategy. This prevents the campaign from serving. For example, a campaign may be attached to a manager bidding strategy and the serving account is subsequently unlinked from the manager account. In this case the campaign will automatically be detached from the now inaccessible manager bidding strategy and transition to the INVALID bidding strategy type.", "Manual bidding strategy that allows advertiser to set the bid per advertiser-specified action.", "Manual click based bidding where user pays per click.", "Manual impression based bidding where user pays per thousand impressions.", "A bidding strategy that pays a configurable amount per video view.", "A bidding strategy that automatically maximizes number of conversions given a daily budget.", "An automated bidding strategy that automatically sets bids to maximize revenue while spending your budget.", "Page-One Promoted bidding scheme, which sets max cpc bids to target impressions on page one or page one promoted slots on google.com. This enum value is deprecated.", "Percent Cpc is bidding strategy where bids are a fraction of the advertised price for some good or service.", "Target CPA is an automated bid strategy that sets bids to help get as many conversions as possible at the target cost-per-acquisition (CPA) you set.", "Target CPM is an automated bid strategy that sets bids to help get as many impressions as possible at the target cost per one thousand impressions (CPM) you set.", "An automated bidding strategy that sets bids so that a certain percentage of search ads are shown at the top of the first page (or other targeted location).", "Target Outrank Share is an automated bidding strategy that sets bids based on the target fraction of auctions where the advertiser should outrank a specific competitor. This enum value is deprecated.", "Target ROAS is an automated bidding strategy that helps you maximize revenue while averaging a specific target Return On Average Spend (ROAS).", "Target Spend is an automated bid strategy that sets your bids to help get as many clicks as possible within your budget."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__Campaign": {"description": "A campaign.", "id": "GoogleAdsSearchads360V0Resources__Campaign", "properties": {"adServingOptimizationStatus": {"description": "The ad serving optimization status of the campaign.", "enum": ["UNSPECIFIED", "UNKNOWN", "OPTIMIZE", "CONVERSION_OPTIMIZE", "ROTATE", "ROTATE_INDEFINITELY", "UNAVAILABLE"], "enumDescriptions": ["No value has been specified.", "The received value is not known in this version. This is a response-only value.", "Ad serving is optimized based on CTR for the campaign.", "Ad serving is optimized based on CTR * Conversion for the campaign. If the campaign is not in the conversion optimizer bidding strategy, it will default to OPTIMIZED.", "Ads are rotated evenly for 90 days, then optimized for clicks.", "Show lower performing ads more evenly with higher performing ads, and do not optimize.", "Ad serving optimization status is not available."], "type": "string"}, "advertisingChannelSubType": {"description": "Immutable. Optional refinement to `advertising_channel_type`. Must be a valid sub-type of the parent channel type. Can be set only when creating campaigns. After campaign is created, the field can not be changed.", "enum": ["UNSPECIFIED", "UNKNOWN", "SEARCH_MOBILE_APP", "DISPLAY_MOBILE_APP", "SEARCH_EXPRESS", "DISPLAY_EXPRESS", "SHOPPING_SMART_ADS", "DISPLAY_GMAIL_AD", "DISPLAY_SMART_CAMPAIGN", "VIDEO_OUTSTREAM", "VIDEO_ACTION", "VIDEO_NON_SKIPPABLE", "APP_CAMPAIGN", "APP_CAMPAIGN_FOR_ENGAGEMENT", "LOCAL_CAMPAIGN", "SHOPPING_COMPARISON_LISTING_ADS", "SMART_CAMPAIGN", "VIDEO_SEQUENCE", "APP_CAMPAIGN_FOR_PRE_REGISTRATION", "VIDEO_REACH_TARGET_FREQUENCY", "TRAVEL_ACTIVITIES"], "enumDescriptions": ["Not specified.", "Used as a return value only. Represents value unknown in this version.", "Mobile app campaigns for Search.", "Mobile app campaigns for Display.", "AdWords express campaigns for search.", "AdWords Express campaigns for display.", "Smart Shopping campaigns.", "Gmail Ad campaigns.", "Smart display campaigns. New campaigns of this sub type cannot be created.", "Video Outstream campaigns.", "Video TrueView for Action campaigns.", "Video campaigns with non-skippable video ads.", "App Campaign that lets you easily promote your Android or iOS app across Google's top properties including Search, Play, YouTube, and the Google Display Network.", "App Campaign for engagement, focused on driving re-engagement with the app across several of Google's top properties including Search, YouTube, and the Google Display Network.", "Campaigns specialized for local advertising.", "Shopping Comparison Listing campaigns.", "Standard Smart campaigns.", "Video campaigns with sequence video ads.", "App Campaign for pre registration, specialized for advertising mobile app pre-registration, that targets multiple advertising channels across Google Play, YouTube and Display Network.", "Video reach campaign with Target Frequency bidding strategy.", "Travel Activities campaigns."], "type": "string"}, "advertisingChannelType": {"description": "Immutable. The primary serving target for ads within the campaign. The targeting options can be refined in `network_settings`. This field is required and should not be empty when creating new campaigns. Can be set only when creating campaigns. After the campaign is created, the field can not be changed.", "enum": ["UNSPECIFIED", "UNKNOWN", "SEARCH", "DISPLAY", "SHOPPING", "HOTEL", "VIDEO", "MULTI_CHANNEL", "LOCAL", "SMART", "PERFORMANCE_MAX", "LOCAL_SERVICES", "DISCOVERY", "TRAVEL"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Search Network. Includes display bundled, and Search+ campaigns.", "Google Display Network only.", "Shopping campaigns serve on the shopping property and on google.com search results.", "Hotel Ads campaigns.", "Video campaigns.", "App Campaigns, and App Campaigns for Engagement, that run across multiple channels.", "Local ads campaigns.", "Smart campaigns.", "Performance Max campaigns.", "Local services campaigns.", "Discovery campaigns.", "Travel campaigns."], "type": "string"}, "biddingStrategy": {"description": "Portfolio bidding strategy used by campaign.", "type": "string"}, "biddingStrategySystemStatus": {"description": "Output only. The system status of the campaign's bidding strategy.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "LEARNING_NEW", "LEARNING_SETTING_CHANGE", "LEARNING_BUDGET_CHANGE", "LEARNING_COMPOSITION_CHANGE", "LEARNING_CONVERSION_TYPE_CHANGE", "LEARNING_CONVERSION_SETTING_CHANGE", "LIMITED_BY_CPC_BID_CEILING", "LIMITED_BY_CPC_BID_FLOOR", "LIMITED_BY_DATA", "LIMITED_BY_BUDGET", "LIMITED_BY_LOW_PRIORITY_SPEND", "LIMITED_BY_LOW_QUALITY", "LIMITED_BY_INVENTORY", "MISCONFIGURED_ZERO_ELIGIBILITY", "MISCONFIGURED_CONVERSION_TYPES", "MISCONFIGURED_CONVERSION_SETTINGS", "MISCONFIGURED_SHARED_BUDGET", "MISCONFIGURED_STRATEGY_TYPE", "PAUSED", "UNAVAILABLE", "MULTIPLE_LEARNING", "MULTIPLE_LIMITED", "MULTIPLE_MISCONFIGURED", "MULTIPLE"], "enumDescriptions": ["Signals that an unexpected error occurred, for example, no bidding strategy type was found, or no status information was found.", "Used for return value only. Represents value unknown in this version.", "The bid strategy is active, and AdWords cannot find any specific issues with the strategy.", "The bid strategy is learning because it has been recently created or recently reactivated.", "The bid strategy is learning because of a recent setting change.", "The bid strategy is learning because of a recent budget change.", "The bid strategy is learning because of recent change in number of campaigns, ad groups or keywords attached to it.", "The bid strategy depends on conversion reporting and the customer recently modified conversion types that were relevant to the bid strategy.", "The bid strategy depends on conversion reporting and the customer recently changed their conversion settings.", "The bid strategy is limited by its bid ceiling.", "The bid strategy is limited by its bid floor.", "The bid strategy is limited because there was not enough conversion traffic over the past weeks.", "A significant fraction of keywords in this bid strategy are limited by budget.", "The bid strategy cannot reach its target spend because its spend has been de-prioritized.", "A significant fraction of keywords in this bid strategy have a low Quality Score.", "The bid strategy cannot fully spend its budget because of narrow targeting.", "Missing conversion tracking (no pings present) and/or remarketing lists for SSC.", "The bid strategy depends on conversion reporting and the customer is lacking conversion types that might be reported against this strategy.", "The bid strategy depends on conversion reporting and the customer's conversion settings are misconfigured.", "There are campaigns outside the bid strategy that share budgets with campaigns included in the strategy.", "The campaign has an invalid strategy type and is not serving.", "The bid strategy is not active. Either there are no active campaigns, ad groups or keywords attached to the bid strategy. Or there are no active budgets connected to the bid strategy.", "This bid strategy currently does not support status reporting.", "There were multiple LEARNING_* system statuses for this bid strategy during the time in question.", "There were multiple LIMITED_* system statuses for this bid strategy during the time in question.", "There were multiple MISCONFIGURED_* system statuses for this bid strategy during the time in question.", "There were multiple system statuses for this bid strategy during the time in question."], "readOnly": true, "type": "string"}, "biddingStrategyType": {"description": "Output only. The type of bidding strategy. A bidding strategy can be created by setting either the bidding scheme to create a standard bidding strategy or the `bidding_strategy` field to create a portfolio bidding strategy. This field is read-only.", "enum": ["UNSPECIFIED", "UNKNOWN", "COMMISSION", "ENHANCED_CPC", "INVALID", "MANUAL_CPA", "MANUAL_CPC", "MANUAL_CPM", "MANUAL_CPV", "MAXIMIZE_CONVERSIONS", "MAXIMIZE_CONVERSION_VALUE", "PAGE_ONE_PROMOTED", "PERCENT_CPC", "TARGET_CPA", "TARGET_CPM", "TARGET_IMPRESSION_SHARE", "TARGET_OUTRANK_SHARE", "TARGET_ROAS", "TARGET_SPEND"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Commission is an automatic bidding strategy in which the advertiser pays a certain portion of the conversion value.", "Enhanced CPC is a bidding strategy that raises bids for clicks that seem more likely to lead to a conversion and lowers them for clicks where they seem less likely.", "Used for return value only. Indicates that a campaign does not have a bidding strategy. This prevents the campaign from serving. For example, a campaign may be attached to a manager bidding strategy and the serving account is subsequently unlinked from the manager account. In this case the campaign will automatically be detached from the now inaccessible manager bidding strategy and transition to the INVALID bidding strategy type.", "Manual bidding strategy that allows advertiser to set the bid per advertiser-specified action.", "Manual click based bidding where user pays per click.", "Manual impression based bidding where user pays per thousand impressions.", "A bidding strategy that pays a configurable amount per video view.", "A bidding strategy that automatically maximizes number of conversions given a daily budget.", "An automated bidding strategy that automatically sets bids to maximize revenue while spending your budget.", "Page-One Promoted bidding scheme, which sets max cpc bids to target impressions on page one or page one promoted slots on google.com. This enum value is deprecated.", "Percent Cpc is bidding strategy where bids are a fraction of the advertised price for some good or service.", "Target CPA is an automated bid strategy that sets bids to help get as many conversions as possible at the target cost-per-acquisition (CPA) you set.", "Target CPM is an automated bid strategy that sets bids to help get as many impressions as possible at the target cost per one thousand impressions (CPM) you set.", "An automated bidding strategy that sets bids so that a certain percentage of search ads are shown at the top of the first page (or other targeted location).", "Target Outrank Share is an automated bidding strategy that sets bids based on the target fraction of auctions where the advertiser should outrank a specific competitor. This enum value is deprecated.", "Target ROAS is an automated bidding strategy that helps you maximize revenue while averaging a specific target Return On Average Spend (ROAS).", "Target Spend is an automated bid strategy that sets your bids to help get as many clicks as possible within your budget."], "readOnly": true, "type": "string"}, "campaignBudget": {"description": "The budget of the campaign.", "type": "string"}, "createTime": {"description": "Output only. The timestamp when this campaign was created. The timestamp is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss\" format. create_time will be deprecated in v1. Use creation_time instead.", "readOnly": true, "type": "string"}, "creationTime": {"description": "Output only. The timestamp when this campaign was created. The timestamp is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss\" format.", "readOnly": true, "type": "string"}, "dynamicSearchAdsSetting": {"$ref": "GoogleAdsSearchads360V0Resources_Campaign_DynamicSearchAdsSetting", "description": "The setting for controlling Dynamic Search Ads (DSA)."}, "endDate": {"description": "The last day of the campaign in serving customer's timezone in YYYY-MM-DD format. On create, defaults to 2037-12-30, which means the campaign will run indefinitely. To set an existing campaign to run indefinitely, set this field to 2037-12-30.", "type": "string"}, "engineId": {"description": "Output only. ID of the campaign in the external engine account. This field is for non-Google Ads account only, for example, Yahoo Japan, Microsoft, Baidu etc. For Google Ads entity, use \"campaign.id\" instead.", "readOnly": true, "type": "string"}, "excludedParentAssetFieldTypes": {"description": "The asset field types that should be excluded from this campaign. Asset links with these field types will not be inherited by this campaign from the upper level.", "items": {"enum": ["UNSPECIFIED", "UNKNOWN", "HEADLINE", "DESCRIPTION", "MANDATORY_AD_TEXT", "MARKETING_IMAGE", "MEDIA_BUNDLE", "YOUTUBE_VIDEO", "BOOK_ON_GOOGLE", "LEAD_FORM", "PROMOTION", "CALLOUT", "STRUCTURED_SNIPPET", "SITELINK", "MOBILE_APP", "HOTEL_CALLOUT", "CALL", "PRICE", "LONG_HEADLINE", "BUSINESS_NAME", "SQUARE_MARKETING_IMAGE", "PORTRAIT_MARKETING_IMAGE", "LOGO", "LANDSCAPE_LOGO", "VIDEO", "CALL_TO_ACTION_SELECTION", "AD_IMAGE", "BUSINESS_LOGO", "HOTEL_PROPERTY"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The asset is linked for use as a headline.", "The asset is linked for use as a description.", "The asset is linked for use as mandatory ad text.", "The asset is linked for use as a marketing image.", "The asset is linked for use as a media bundle.", "The asset is linked for use as a YouTube video.", "The asset is linked to indicate that a hotels campaign is \"Book on Google\" enabled.", "The asset is linked for use as a Lead Form extension.", "The asset is linked for use as a Promotion extension.", "The asset is linked for use as a Callout extension.", "The asset is linked for use as a Structured Snippet extension.", "The asset is linked for use as a Sitelink.", "The asset is linked for use as a Mobile App extension.", "The asset is linked for use as a Hotel Callout extension.", "The asset is linked for use as a Call extension.", "The asset is linked for use as a Price extension.", "The asset is linked for use as a long headline.", "The asset is linked for use as a business name.", "The asset is linked for use as a square marketing image.", "The asset is linked for use as a portrait marketing image.", "The asset is linked for use as a logo.", "The asset is linked for use as a landscape logo.", "The asset is linked for use as a non YouTube logo.", "The asset is linked for use to select a call-to-action.", "The asset is linked for use to select an ad image.", "The asset is linked for use as a business logo.", "The asset is linked for use as a hotel property in a Performance Max for travel goals campaign."], "type": "string"}, "type": "array"}, "finalUrlSuffix": {"description": "Suffix used to append query parameters to landing pages that are served with parallel tracking.", "type": "string"}, "frequencyCaps": {"description": "A list that limits how often each user will see this campaign's ads.", "items": {"$ref": "GoogleAdsSearchads360V0Common__FrequencyCapEntry"}, "type": "array"}, "geoTargetTypeSetting": {"$ref": "GoogleAdsSearchads360V0Resources_Campaign_GeoTargetTypeSetting", "description": "The setting for ads geotargeting."}, "id": {"description": "Output only. The ID of the campaign.", "format": "int64", "readOnly": true, "type": "string"}, "labels": {"description": "Output only. The resource names of labels attached to this campaign.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "lastModifiedTime": {"description": "Output only. The datetime when this campaign was last modified. The datetime is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss.ssssss\" format.", "readOnly": true, "type": "string"}, "manualCpa": {"$ref": "GoogleAdsSearchads360V0Common__ManualCpa", "description": "Standard Manual CPA bidding strategy. Manual bidding strategy that allows advertiser to set the bid per advertiser-specified action. Supported only for Local Services campaigns."}, "manualCpc": {"$ref": "GoogleAdsSearchads360V0Common__ManualCpc", "description": "Standard Manual CPC bidding strategy. Manual click-based bidding where user pays per click."}, "manualCpm": {"$ref": "GoogleAdsSearchads360V0Common__ManualCpm", "description": "Standard Manual CPM bidding strategy. Manual impression-based bidding where user pays per thousand impressions."}, "maximizeConversionValue": {"$ref": "GoogleAdsSearchads360V0Common__MaximizeConversionValue", "description": "Standard Maximize Conversion Value bidding strategy that automatically sets bids to maximize revenue while spending your budget."}, "maximizeConversions": {"$ref": "GoogleAdsSearchads360V0Common__MaximizeConversions", "description": "Standard Maximize Conversions bidding strategy that automatically maximizes number of conversions while spending your budget."}, "name": {"description": "The name of the campaign. This field is required and should not be empty when creating new campaigns. It must not contain any null (code point 0x0), NL line feed (code point 0xA) or carriage return (code point 0xD) characters.", "type": "string"}, "networkSettings": {"$ref": "GoogleAdsSearchads360V0Resources_Campaign_NetworkSettings", "description": "The network settings for the campaign."}, "optimizationGoalSetting": {"$ref": "GoogleAdsSearchads360V0Resources_Campaign_OptimizationGoalSetting", "description": "Optimization goal setting for this campaign, which includes a set of optimization goal types."}, "percentCpc": {"$ref": "GoogleAdsSearchads360V0Common__PercentCpc", "description": "Standard Percent Cpc bidding strategy where bids are a fraction of the advertised price for some good or service."}, "realTimeBiddingSetting": {"$ref": "GoogleAdsSearchads360V0Common__RealTimeBiddingSetting", "description": "Settings for Real-Time Bidding, a feature only available for campaigns targeting the Ad Exchange network."}, "resourceName": {"description": "Immutable. The resource name of the campaign. Campaign resource names have the form: `customers/{customer_id}/campaigns/{campaign_id}`", "type": "string"}, "selectiveOptimization": {"$ref": "GoogleAdsSearchads360V0Resources_Campaign_SelectiveOptimization", "description": "Selective optimization setting for this campaign, which includes a set of conversion actions to optimize this campaign towards. This feature only applies to app campaigns that use MULTI_CHANNEL as AdvertisingChannelType and APP_CAMPAIGN or APP_CAMPAIGN_FOR_ENGAGEMENT as AdvertisingChannelSubType."}, "servingStatus": {"description": "Output only. The ad serving status of the campaign.", "enum": ["UNSPECIFIED", "UNKNOWN", "SERVING", "NONE", "ENDED", "PENDING", "SUSPENDED"], "enumDescriptions": ["No value has been specified.", "The received value is not known in this version. This is a response-only value.", "Serving.", "None.", "Ended.", "Pending.", "Suspended."], "readOnly": true, "type": "string"}, "shoppingSetting": {"$ref": "GoogleAdsSearchads360V0Resources_Campaign_ShoppingSetting", "description": "The setting for controlling Shopping campaigns."}, "startDate": {"description": "The date when campaign started in serving customer's timezone in YYYY-MM-DD format.", "type": "string"}, "status": {"description": "The status of the campaign. When a new campaign is added, the status defaults to ENABLED.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "PAUSED", "REMOVED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Campaign is active and can show ads.", "Campaign has been paused by the user.", "Campaign has been removed."], "type": "string"}, "targetCpa": {"$ref": "GoogleAdsSearchads360V0Common__TargetCpa", "description": "Standard Target CPA bidding strategy that automatically sets bids to help get as many conversions as possible at the target cost-per-acquisition (CPA) you set."}, "targetCpm": {"$ref": "GoogleAdsSearchads360V0Common__TargetCpm", "description": "A bidding strategy that automatically optimizes cost per thousand impressions."}, "targetImpressionShare": {"$ref": "GoogleAdsSearchads360V0Common__TargetImpressionShare", "description": "Target Impression Share bidding strategy. An automated bidding strategy that sets bids to achieve a chosen percentage of impressions."}, "targetRoas": {"$ref": "GoogleAdsSearchads360V0Common__TargetRoas", "description": "Standard Target ROAS bidding strategy that automatically maximizes revenue while averaging a specific target return on ad spend (ROAS)."}, "targetSpend": {"$ref": "GoogleAdsSearchads360V0Common__TargetSpend", "description": "Standard Target Spend bidding strategy that automatically sets your bids to help get as many clicks as possible within your budget."}, "trackingSetting": {"$ref": "GoogleAdsSearchads360V0Resources_Campaign_TrackingSetting", "description": "Output only. Campaign-level settings for tracking information.", "readOnly": true}, "trackingUrlTemplate": {"description": "The URL template for constructing a tracking URL.", "type": "string"}, "urlCustomParameters": {"description": "The list of mappings used to substitute custom parameter tags in a `tracking_url_template`, `final_urls`, or `mobile_final_urls`.", "items": {"$ref": "GoogleAdsSearchads360V0Common__CustomParameter"}, "type": "array"}, "urlExpansionOptOut": {"description": "Represents opting out of URL expansion to more targeted URLs. If opted out (true), only the final URLs in the asset group or URLs specified in the advertiser's Google Merchant Center or business data feeds are targeted. If opted in (false), the entire domain will be targeted. This field can only be set for Performance Max campaigns, where the default value is false.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CampaignAsset": {"description": "A link between a Campaign and an Asset.", "id": "GoogleAdsSearchads360V0Resources__CampaignAsset", "properties": {"asset": {"description": "Immutable. The asset which is linked to the campaign.", "type": "string"}, "campaign": {"description": "Immutable. The campaign to which the asset is linked.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the campaign asset. CampaignAsset resource names have the form: `customers/{customer_id}/campaignAssets/{campaign_id}~{asset_id}~{field_type}`", "type": "string"}, "status": {"description": "Output only. Status of the campaign asset.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED", "PAUSED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Asset link is enabled.", "Asset link has been removed.", "Asset link is paused."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CampaignAssetSet": {"description": "CampaignAssetSet is the linkage between a campaign and an asset set. Adding a CampaignAssetSet links an asset set with a campaign.", "id": "GoogleAdsSearchads360V0Resources__CampaignAssetSet", "properties": {"assetSet": {"description": "Immutable. The asset set which is linked to the campaign.", "type": "string"}, "campaign": {"description": "Immutable. The campaign to which this asset set is linked.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the campaign asset set. Asset set asset resource names have the form: `customers/{customer_id}/campaignAssetSets/{campaign_id}~{asset_set_id}`", "type": "string"}, "status": {"description": "Output only. The status of the campaign asset set asset. Read-only.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED"], "enumDescriptions": ["The status has not been specified.", "The received value is not known in this version. This is a response-only value.", "The linkage between asset set and its container is enabled.", "The linkage between asset set and its container is removed."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CampaignAudienceView": {"description": "A campaign audience view. Includes performance data from interests and remarketing lists for Display Network and YouTube Network ads, and remarketing lists for search ads (RLSA), aggregated by campaign and audience criterion. This view only includes audiences attached at the campaign level.", "id": "GoogleAdsSearchads360V0Resources__CampaignAudienceView", "properties": {"resourceName": {"description": "Output only. The resource name of the campaign audience view. Campaign audience view resource names have the form: `customers/{customer_id}/campaignAudienceViews/{campaign_id}~{criterion_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CampaignBudget": {"description": "A campaign budget.", "id": "GoogleAdsSearchads360V0Resources__CampaignBudget", "properties": {"amountMicros": {"description": "The amount of the budget, in the local currency for the account. Amount is specified in micros, where one million is equivalent to one currency unit. Monthly spend is capped at 30.4 times this amount.", "format": "int64", "type": "string"}, "deliveryMethod": {"description": "The delivery method that determines the rate at which the campaign budget is spent. Defaults to STANDARD if unspecified in a create operation.", "enum": ["UNSPECIFIED", "UNKNOWN", "STANDARD", "ACCELERATED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The budget server will throttle serving evenly across the entire time period.", "The budget server will not throttle serving, and ads will serve as fast as possible."], "type": "string"}, "period": {"description": "Immutable. Period over which to spend the budget. Defaults to DAILY if not specified.", "enum": ["UNSPECIFIED", "UNKNOWN", "DAILY", "FIXED_DAILY", "CUSTOM_PERIOD"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Daily budget.", "Fixed daily budget.", "Custom budget can be used with total_amount to specify lifetime budget limit."], "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the campaign budget. Campaign budget resource names have the form: `customers/{customer_id}/campaignBudgets/{campaign_budget_id}`", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CampaignCriterion": {"description": "A campaign criterion.", "id": "GoogleAdsSearchads360V0Resources__CampaignCriterion", "properties": {"ageRange": {"$ref": "GoogleAdsSearchads360V0Common__AgeRangeInfo", "description": "Immutable. Age range."}, "bidModifier": {"description": "The modifier for the bids when the criterion matches. The modifier must be in the range: 0.1 - 10.0. Most targetable criteria types support modifiers. Use 0 to opt out of a Device type.", "format": "float", "type": "number"}, "criterionId": {"description": "Output only. The ID of the criterion. This field is ignored during mutate.", "format": "int64", "readOnly": true, "type": "string"}, "device": {"$ref": "GoogleAdsSearchads360V0Common__DeviceInfo", "description": "Immutable. Device."}, "displayName": {"description": "Output only. The display name of the criterion. This field is ignored for mutates.", "readOnly": true, "type": "string"}, "gender": {"$ref": "GoogleAdsSearchads360V0Common__GenderInfo", "description": "Immutable. Gender."}, "keyword": {"$ref": "GoogleAdsSearchads360V0Common__KeywordInfo", "description": "Immutable. Keyword."}, "language": {"$ref": "GoogleAdsSearchads360V0Common__LanguageInfo", "description": "Immutable. Language."}, "lastModifiedTime": {"description": "Output only. The datetime when this campaign criterion was last modified. The datetime is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss.ssssss\" format.", "readOnly": true, "type": "string"}, "location": {"$ref": "GoogleAdsSearchads360V0Common__LocationInfo", "description": "Immutable. Location."}, "locationGroup": {"$ref": "GoogleAdsSearchads360V0Common__LocationGroupInfo", "description": "Immutable. Location Group"}, "negative": {"description": "Immutable. Whether to target (`false`) or exclude (`true`) the criterion.", "type": "boolean"}, "resourceName": {"description": "Immutable. The resource name of the campaign criterion. Campaign criterion resource names have the form: `customers/{customer_id}/campaignCriteria/{campaign_id}~{criterion_id}`", "type": "string"}, "status": {"description": "The status of the criterion.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "PAUSED", "REMOVED"], "enumDescriptions": ["No value has been specified.", "The received value is not known in this version. This is a response-only value.", "The campaign criterion is enabled.", "The campaign criterion is paused.", "The campaign criterion is removed."], "type": "string"}, "type": {"description": "Output only. The type of the criterion.", "enum": ["UNSPECIFIED", "UNKNOWN", "KEYWORD", "PLACEMENT", "MOBILE_APP_CATEGORY", "MOBILE_APPLICATION", "DEVICE", "LOCATION", "LISTING_GROUP", "AD_SCHEDULE", "AGE_RANGE", "GENDER", "INCOME_RANGE", "PARENTAL_STATUS", "YOUTUBE_VIDEO", "YOUTUBE_CHANNEL", "USER_LIST", "PROXIMITY", "TOPIC", "LISTING_SCOPE", "LANGUAGE", "IP_BLOCK", "CONTENT_LABEL", "CARRIER", "USER_INTEREST", "WEBPAGE", "OPERATING_SYSTEM_VERSION", "APP_PAYMENT_MODEL", "MOBILE_DEVICE", "CUSTOM_AFFINITY", "CUSTOM_INTENT", "LOCATION_GROUP", "CUSTOM_AUDIENCE", "COMBINED_AUDIENCE", "KEYWORD_THEME", "AUDIENCE", "LOCAL_SERVICE_ID", "BRAND", "BRAND_LIST"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Keyword, for example, 'mars cruise'.", "Placement, also known as Website, for example, 'www.flowers4sale.com'", "Mobile application categories to target.", "Mobile applications to target.", "Devices to target.", "Locations to target.", "Listing groups to target.", "Ad Schedule.", "Age range.", "Gender.", "Income Range.", "Parental status.", "YouTube Video.", "YouTube Channel.", "User list.", "Proximity.", "A topic target on the display network (for example, \"Pets & Animals\").", "Listing scope to target.", "Language.", "<PERSON><PERSON><PERSON><PERSON>.", "Content Label for category exclusion.", "Carrier.", "A category the user is interested in.", "Webpage criterion for dynamic search ads.", "Operating system version.", "App payment model.", "Mobile device.", "Custom affinity.", "Custom intent.", "Location group.", "Custom audience", "Combined audience", "Smart Campaign keyword theme", "Audience", "Local Services Ads Service ID.", "Brand", "Brand List"], "readOnly": true, "type": "string"}, "userList": {"$ref": "GoogleAdsSearchads360V0Common__UserListInfo", "description": "Immutable. User List. The Similar Audiences sunset starts May 2023. Refer to https://ads-developers.googleblog.com/2022/11/announcing-deprecation-and-sunset-of.html for other options."}, "webpage": {"$ref": "GoogleAdsSearchads360V0Common__WebpageInfo", "description": "Immutable. Webpage."}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CampaignLabel": {"description": "Represents a relationship between a campaign and a label.", "id": "GoogleAdsSearchads360V0Resources__CampaignLabel", "properties": {"campaign": {"description": "Immutable. The campaign to which the label is attached.", "type": "string"}, "label": {"description": "Immutable. The label assigned to the campaign.", "type": "string"}, "resourceName": {"description": "Immutable. Name of the resource. Campaign label resource names have the form: `customers/{customer_id}/campaignLabels/{campaign_id}~{label_id}`", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CartDataSalesView": {"description": "Cart data sales view.", "id": "GoogleAdsSearchads360V0Resources__CartDataSalesView", "properties": {"resourceName": {"description": "Output only. The resource name of the Cart data sales view. Cart data sales view resource names have the form: `customers/{customer_id}/cartDataSalesView`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__Conversion": {"description": "A conversion.", "id": "GoogleAdsSearchads360V0Resources__Conversion", "properties": {"adId": {"description": "Output only. Ad ID. A value of 0 indicates that the ad is unattributed.", "format": "int64", "readOnly": true, "type": "string"}, "advertiserConversionId": {"description": "Output only. For offline conversions, this is an ID provided by advertisers. If an advertiser doesn't specify such an ID, Search Ads 360 generates one. For online conversions, this is equal to the id column or the floodlight_order_id column depending on the advertiser's Floodlight instructions.", "readOnly": true, "type": "string"}, "assetFieldType": {"description": "Output only. Asset field type of the conversion event.", "enum": ["UNSPECIFIED", "UNKNOWN", "HEADLINE", "DESCRIPTION", "MANDATORY_AD_TEXT", "MARKETING_IMAGE", "MEDIA_BUNDLE", "YOUTUBE_VIDEO", "BOOK_ON_GOOGLE", "LEAD_FORM", "PROMOTION", "CALLOUT", "STRUCTURED_SNIPPET", "SITELINK", "MOBILE_APP", "HOTEL_CALLOUT", "CALL", "PRICE", "LONG_HEADLINE", "BUSINESS_NAME", "SQUARE_MARKETING_IMAGE", "PORTRAIT_MARKETING_IMAGE", "LOGO", "LANDSCAPE_LOGO", "VIDEO", "CALL_TO_ACTION_SELECTION", "AD_IMAGE", "BUSINESS_LOGO", "HOTEL_PROPERTY"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The asset is linked for use as a headline.", "The asset is linked for use as a description.", "The asset is linked for use as mandatory ad text.", "The asset is linked for use as a marketing image.", "The asset is linked for use as a media bundle.", "The asset is linked for use as a YouTube video.", "The asset is linked to indicate that a hotels campaign is \"Book on Google\" enabled.", "The asset is linked for use as a Lead Form extension.", "The asset is linked for use as a Promotion extension.", "The asset is linked for use as a Callout extension.", "The asset is linked for use as a Structured Snippet extension.", "The asset is linked for use as a Sitelink.", "The asset is linked for use as a Mobile App extension.", "The asset is linked for use as a Hotel Callout extension.", "The asset is linked for use as a Call extension.", "The asset is linked for use as a Price extension.", "The asset is linked for use as a long headline.", "The asset is linked for use as a business name.", "The asset is linked for use as a square marketing image.", "The asset is linked for use as a portrait marketing image.", "The asset is linked for use as a logo.", "The asset is linked for use as a landscape logo.", "The asset is linked for use as a non YouTube logo.", "The asset is linked for use to select a call-to-action.", "The asset is linked for use to select an ad image.", "The asset is linked for use as a business logo.", "The asset is linked for use as a hotel property in a Performance Max for travel goals campaign."], "readOnly": true, "type": "string"}, "assetId": {"description": "Output only. ID of the asset which was interacted with during the conversion event.", "format": "int64", "readOnly": true, "type": "string"}, "attributionType": {"description": "Output only. What the conversion is attributed to: Visit or Keyword+Ad.", "enum": ["UNSPECIFIED", "UNKNOWN", "VISIT", "CRITERION_AD"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The conversion is attributed to a visit.", "The conversion is attributed to a criterion and ad pair."], "readOnly": true, "type": "string"}, "clickId": {"description": "Output only. A unique string, for the visit that the conversion is attributed to, that is passed to the landing page as the click id URL parameter.", "readOnly": true, "type": "string"}, "conversionDateTime": {"description": "Output only. The timestamp of the conversion event.", "readOnly": true, "type": "string"}, "conversionLastModifiedDateTime": {"description": "Output only. The timestamp of the last time the conversion was modified.", "readOnly": true, "type": "string"}, "conversionQuantity": {"description": "Output only. The quantity of items recorded by the conversion, as determined by the qty url parameter. The advertiser is responsible for dynamically populating the parameter (such as number of items sold in the conversion), otherwise it defaults to 1.", "format": "int64", "readOnly": true, "type": "string"}, "conversionRevenueMicros": {"description": "Output only. The adjusted revenue in micros for the conversion event. This will always be in the currency of the serving account.", "format": "int64", "readOnly": true, "type": "string"}, "conversionVisitDateTime": {"description": "Output only. The timestamp of the visit that the conversion is attributed to.", "readOnly": true, "type": "string"}, "criterionId": {"description": "Output only. Search Ads 360 criterion ID. A value of 0 indicates that the criterion is unattributed.", "format": "int64", "readOnly": true, "type": "string"}, "floodlightOrderId": {"description": "Output only. The Floodlight order ID provided by the advertiser for the conversion.", "readOnly": true, "type": "string"}, "floodlightOriginalRevenue": {"description": "Output only. The original, unchanged revenue associated with the Floodlight event (in the currency of the current report), before Floodlight currency instruction modifications.", "format": "int64", "readOnly": true, "type": "string"}, "id": {"description": "Output only. The ID of the conversion", "format": "int64", "readOnly": true, "type": "string"}, "merchantId": {"description": "Output only. The SearchAds360 inventory account ID containing the product that was clicked on. SearchAds360 generates this ID when you link an inventory account in SearchAds360.", "format": "int64", "readOnly": true, "type": "string"}, "productChannel": {"description": "Output only. The sales channel of the product that was clicked on: Online or Local.", "enum": ["UNSPECIFIED", "UNKNOWN", "ONLINE", "LOCAL"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The item is sold online.", "The item is sold in local stores."], "readOnly": true, "type": "string"}, "productCountryCode": {"description": "Output only. The country (ISO-3166-format) registered for the inventory feed that contains the product clicked on.", "readOnly": true, "type": "string"}, "productId": {"description": "Output only. The ID of the product clicked on.", "readOnly": true, "type": "string"}, "productLanguageCode": {"description": "Output only. The language (ISO-639-1) that has been set for the Merchant Center feed containing data about the product.", "readOnly": true, "type": "string"}, "productStoreId": {"description": "Output only. The store in the Local Inventory Ad that was clicked on. This should match the store IDs used in your local products feed.", "readOnly": true, "type": "string"}, "resourceName": {"description": "Output only. The resource name of the conversion. Conversion resource names have the form: `customers/{customer_id}/conversions/{ad_group_id}~{criterion_id}~{ds_conversion_id}`", "readOnly": true, "type": "string"}, "status": {"description": "Output only. The status of the conversion, either ENABLED or REMOVED..", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The conversion is enabled.", "The conversion has been removed."], "readOnly": true, "type": "string"}, "visitId": {"description": "Output only. The SearchAds360 visit ID that the conversion is attributed to.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__ConversionAction": {"description": "A conversion action.", "id": "GoogleAdsSearchads360V0Resources__ConversionAction", "properties": {"appId": {"description": "App ID for an app conversion action.", "type": "string"}, "attributionModelSettings": {"$ref": "GoogleAdsSearchads360V0Resources_ConversionAction_AttributionModelSettings", "description": "Settings related to this conversion action's attribution model."}, "category": {"description": "The category of conversions reported for this conversion action.", "enum": ["UNSPECIFIED", "UNKNOWN", "DEFAULT", "PAGE_VIEW", "PURCHASE", "SIGNUP", "LEAD", "DOWNLOAD", "ADD_TO_CART", "BEGIN_CHECKOUT", "SUBSCRIBE_PAID", "PHONE_CALL_LEAD", "IMPORTED_LEAD", "SUBMIT_LEAD_FORM", "BOOK_APPOINTMENT", "REQUEST_QUOTE", "GET_DIRECTIONS", "OUTBOUND_CLICK", "CONTACT", "ENGAGEMENT", "STORE_VISIT", "STORE_SALE", "QUALIFIED_LEAD", "CONVERTED_LEAD"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Default category.", "User visiting a page.", "Purchase, sales, or \"order placed\" event.", "Signup user action.", "Lead-generating action.", "Software download action (as for an app).", "The addition of items to a shopping cart or bag on an advertiser site.", "When someone enters the checkout flow on an advertiser site.", "The start of a paid subscription for a product or service.", "A call to indicate interest in an advertiser's offering.", "A lead conversion imported from an external source into Google Ads.", "A submission of a form on an advertiser site indicating business interest.", "A booking of an appointment with an advertiser's business.", "A quote or price estimate request.", "A search for an advertiser's business location with intention to visit.", "A click to an advertiser's partner's site.", "A call, SMS, email, chat or other type of contact to an advertiser.", "A website engagement event such as long site time or a Google Analytics (GA) Smart Goal. Intended to be used for GA, Firebase, GA Gold goal imports.", "A visit to a physical store location.", "A sale occurring in a physical store.", "A lead conversion imported from an external source into Google Ads, that has been further qualified by the advertiser (marketing/sales team). In the lead-to-sale journey, advertisers get leads, then act on them by reaching out to the consumer. If the consumer is interested and may end up buying their product, the advertiser marks such leads as \"qualified leads\".", "A lead conversion imported from an external source into Google Ads, that has further completed a chosen stage as defined by the lead gen advertiser."], "type": "string"}, "clickThroughLookbackWindowDays": {"description": "The maximum number of days that may elapse between an interaction (for example, a click) and a conversion event.", "format": "int64", "type": "string"}, "creationTime": {"description": "Output only. Timestamp of the Floodlight activity's creation, formatted in ISO 8601.", "readOnly": true, "type": "string"}, "floodlightSettings": {"$ref": "GoogleAdsSearchads360V0Resources_ConversionAction_FloodlightSettings", "description": "Output only. Floodlight settings for Floodlight conversion types.", "readOnly": true}, "id": {"description": "Output only. The ID of the conversion action.", "format": "int64", "readOnly": true, "type": "string"}, "includeInClientAccountConversionsMetric": {"description": "Whether this conversion action should be included in the \"client_account_conversions\" metric.", "type": "boolean"}, "includeInConversionsMetric": {"description": "Output only. Whether this conversion action should be included in the \"conversions\" metric.", "readOnly": true, "type": "boolean"}, "name": {"description": "The name of the conversion action. This field is required and should not be empty when creating new conversion actions.", "type": "string"}, "ownerCustomer": {"description": "Output only. The resource name of the conversion action owner customer, or null if this is a system-defined conversion action.", "readOnly": true, "type": "string"}, "primaryForGoal": {"description": "If a conversion action's primary_for_goal bit is false, the conversion action is non-biddable for all campaigns regardless of their customer conversion goal or campaign conversion goal. However, custom conversion goals do not respect primary_for_goal, so if a campaign has a custom conversion goal configured with a primary_for_goal = false conversion action, that conversion action is still biddable. By default, primary_for_goal will be true if not set. In V9, primary_for_goal can only be set to false after creation through an 'update' operation because it's not declared as optional.", "type": "boolean"}, "resourceName": {"description": "Immutable. The resource name of the conversion action. Conversion action resource names have the form: `customers/{customer_id}/conversionActions/{conversion_action_id}`", "type": "string"}, "status": {"description": "The status of this conversion action for conversion event accrual.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED", "HIDDEN"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Conversions will be recorded.", "Conversions will not be recorded.", "Conversions will not be recorded and the conversion action will not appear in the UI."], "type": "string"}, "type": {"description": "Immutable. The type of this conversion action.", "enum": ["UNSPECIFIED", "UNKNOWN", "AD_CALL", "CLICK_TO_CALL", "GOOGLE_PLAY_DOWNLOAD", "GOOGLE_PLAY_IN_APP_PURCHASE", "UPLOAD_CALLS", "UPLOAD_CLICKS", "WEBPAGE", "WEBSITE_CALL", "STORE_SALES_DIRECT_UPLOAD", "STORE_SALES", "FIREBASE_ANDROID_FIRST_OPEN", "FIREBASE_ANDROID_IN_APP_PURCHASE", "FIREBASE_ANDROID_CUSTOM", "FIREBASE_IOS_FIRST_OPEN", "FIREBASE_IOS_IN_APP_PURCHASE", "FIREBASE_IOS_CUSTOM", "THIRD_PARTY_APP_ANALYTICS_ANDROID_FIRST_OPEN", "THIRD_PARTY_APP_ANALYTICS_ANDROID_IN_APP_PURCHASE", "THIRD_PARTY_APP_ANALYTICS_ANDROID_CUSTOM", "THIRD_PARTY_APP_ANALYTICS_IOS_FIRST_OPEN", "THIRD_PARTY_APP_ANALYTICS_IOS_IN_APP_PURCHASE", "THIRD_PARTY_APP_ANALYTICS_IOS_CUSTOM", "ANDROID_APP_PRE_REGISTRATION", "ANDROID_INSTALLS_ALL_OTHER_APPS", "FLOODLIGHT_ACTION", "FLOODLIGHT_TRANSACTION", "GOOGLE_HOSTED", "LEAD_FORM_SUBMIT", "SALESFORCE", "SEARCH_ADS_360", "SMART_CAMPAIGN_AD_CLICKS_TO_CALL", "SMART_CAMPAIGN_MAP_CLICKS_TO_CALL", "SMART_CAMPAIGN_MAP_DIRECTIONS", "SMART_CAMPAIGN_TRACKED_CALLS", "STORE_VISITS", "WEBPAGE_CODELESS", "UNIVERSAL_ANALYTICS_GOAL", "UNIVERSAL_ANALYTICS_TRANSACTION", "GOOGLE_ANALYTICS_4_CUSTOM", "GOOGLE_ANALYTICS_4_PURCHASE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Conversions that occur when a user clicks on an ad's call extension.", "Conversions that occur when a user on a mobile device clicks a phone number.", "Conversions that occur when a user downloads a mobile app from the Google Play Store.", "Conversions that occur when a user makes a purchase in an app through Android billing.", "Call conversions that are tracked by the advertiser and uploaded.", "Conversions that are tracked by the advertiser and uploaded with attributed clicks.", "Conversions that occur on a webpage.", "Conversions that occur when a user calls a dynamically-generated phone number from an advertiser's website.", "Store Sales conversion based on first-party or third-party merchant data uploads. Only customers on the allowlist can use store sales direct upload types.", "Store Sales conversion based on first-party or third-party merchant data uploads and/or from in-store purchases using cards from payment networks. Only customers on the allowlist can use store sales types. Read only.", "Android app first open conversions tracked through Firebase.", "Android app in app purchase conversions tracked through Firebase.", "Android app custom conversions tracked through Firebase.", "iOS app first open conversions tracked through Firebase.", "iOS app in app purchase conversions tracked through Firebase.", "iOS app custom conversions tracked through Firebase.", "Android app first open conversions tracked through Third Party App Analytics.", "Android app in app purchase conversions tracked through Third Party App Analytics.", "Android app custom conversions tracked through Third Party App Analytics.", "iOS app first open conversions tracked through Third Party App Analytics.", "iOS app in app purchase conversions tracked through Third Party App Analytics.", "iOS app custom conversions tracked through Third Party App Analytics.", "Conversions that occur when a user pre-registers a mobile app from the Google Play Store. Read only.", "Conversions that track all Google Play downloads which aren't tracked by an app-specific type. Read only.", "Floodlight activity that counts the number of times that users have visited a particular webpage after seeing or clicking on one of an advertiser's ads. Read only.", "Floodlight activity that tracks the number of sales made or the number of items purchased. Can also capture the total value of each sale. Read only.", "Conversions that track local actions from Google's products and services after interacting with an ad. Read only.", "Conversions reported when a user submits a lead form. Read only.", "Conversions that come from Salesforce. Read only.", "Conversions imported from Search Ads 360 Floodlight data. Read only.", "Call conversions that occur on Smart campaign Ads without call tracking setup, using Smart campaign custom criteria. Read only.", "The user clicks on a call element within Google Maps. Smart campaign only. Read only.", "The user requests directions to a business location within Google Maps. Smart campaign only. Read only.", "Call conversions that occur on Smart campaign Ads with call tracking setup, using Smart campaign custom criteria. Read only.", "Conversions that occur when a user visits an advertiser's retail store. Read only.", "Conversions created from website events (such as form submissions or page loads), that don't use individually coded event snippets. Read only.", "Conversions that come from linked Universal Analytics goals.", "Conversions that come from linked Universal Analytics transactions.", "Conversions that come from linked Google Analytics 4 custom event conversions.", "Conversions that come from linked Google Analytics 4 purchase conversions."], "type": "string"}, "valueSettings": {"$ref": "GoogleAdsSearchads360V0Resources_ConversionAction_ValueSettings", "description": "Settings related to the value for conversion events associated with this conversion action."}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__ConversionTrackingSetting": {"description": "A collection of customer-wide settings related to Search Ads 360 Conversion Tracking.", "id": "GoogleAdsSearchads360V0Resources__ConversionTrackingSetting", "properties": {"acceptedCustomerDataTerms": {"description": "Output only. Whether the customer has accepted customer data terms. If using cross-account conversion tracking, this value is inherited from the manager. This field is read-only. For more information, see https://support.google.com/adspolicy/answer/7475709.", "readOnly": true, "type": "boolean"}, "conversionTrackingId": {"description": "Output only. The conversion tracking id used for this account. This id doesn't indicate whether the customer uses conversion tracking (conversion_tracking_status does). This field is read-only.", "format": "int64", "readOnly": true, "type": "string"}, "conversionTrackingStatus": {"description": "Output only. Conversion tracking status. It indicates whether the customer is using conversion tracking, and who is the conversion tracking owner of this customer. If this customer is using cross-account conversion tracking, the value returned will differ based on the `login-customer-id` of the request.", "enum": ["UNSPECIFIED", "UNKNOWN", "NOT_CONVERSION_TRACKED", "CONVERSION_TRACKING_MANAGED_BY_SELF", "CONVERSION_TRACKING_MANAGED_BY_THIS_MANAGER", "CONVERSION_TRACKING_MANAGED_BY_ANOTHER_MANAGER"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Customer does not use any conversion tracking.", "The conversion actions are created and managed by this customer.", "The conversion actions are created and managed by the manager specified in the request's `login-customer-id`.", "The conversion actions are created and managed by a manager different from the customer or manager specified in the request's `login-customer-id`."], "readOnly": true, "type": "string"}, "crossAccountConversionTrackingId": {"description": "Output only. The conversion tracking id of the customer's manager. This is set when the customer is opted into cross-account conversion tracking, and it overrides conversion_tracking_id.", "format": "int64", "readOnly": true, "type": "string"}, "enhancedConversionsForLeadsEnabled": {"description": "Output only. Whether the customer is opted-in for enhanced conversions for leads. If using cross-account conversion tracking, this value is inherited from the manager. This field is read-only.", "readOnly": true, "type": "boolean"}, "googleAdsConversionCustomer": {"description": "Output only. The resource name of the customer where conversions are created and managed. This field is read-only.", "readOnly": true, "type": "string"}, "googleAdsCrossAccountConversionTrackingId": {"description": "Output only. The conversion tracking id of the customer's manager. This is set when the customer is opted into conversion tracking, and it overrides conversion_tracking_id. This field can only be managed through the Google Ads UI. This field is read-only.", "format": "int64", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CustomColumn": {"description": "A custom column. See Search Ads 360 custom column at https://support.google.com/sa360/answer/9633916", "id": "GoogleAdsSearchads360V0Resources__CustomColumn", "properties": {"description": {"description": "Output only. User-defined description of the custom column.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. ID of the custom column.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "Output only. User-defined name of the custom column.", "readOnly": true, "type": "string"}, "queryable": {"description": "Output only. True when the custom column is available to be used in the query of SearchAds360Service.Search and SearchAds360Service.SearchStream.", "readOnly": true, "type": "boolean"}, "referencedSystemColumns": {"description": "Output only. The list of the referenced system columns of this custom column. For example, A custom column \"sum of impressions and clicks\" has referenced system columns of {\"metrics.clicks\", \"metrics.impressions\"}.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "referencesAttributes": {"description": "Output only. True when the custom column is referring to one or more attributes.", "readOnly": true, "type": "boolean"}, "referencesMetrics": {"description": "Output only. True when the custom column is referring to one or more metrics.", "readOnly": true, "type": "boolean"}, "resourceName": {"description": "Immutable. The resource name of the custom column. Custom column resource names have the form: `customers/{customer_id}/customColumns/{custom_column_id}`", "type": "string"}, "valueType": {"description": "Output only. The type of the result value of the custom column.", "enum": ["UNSPECIFIED", "UNKNOWN", "STRING", "INT64", "DOUBLE", "BOOLEAN"], "enumDescriptions": ["Not specified.", "Unknown.", "The custom column value is a string.", "The custom column value is an int64 number.", "The custom column value is a double number.", "The custom column value is a boolean."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__Customer": {"description": "A customer.", "id": "GoogleAdsSearchads360V0Resources__Customer", "properties": {"accountStatus": {"description": "Output only. Account status, for example, Enabled, Paused, Removed, etc.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "PAUSED", "SUSPENDED", "REMOVED", "DRAFT"], "enumDescriptions": ["Default value.", "Unknown value.", "Account is able to serve ads.", "Account is deactivated by the user.", "Account is deactivated by an internal process.", "Account is irrevocably deactivated.", "Account is still in the process of setup, not ENABLED yet."], "readOnly": true, "type": "string"}, "accountType": {"description": "Output only. Engine account type, for example, Google Ads, Microsoft Advertising, Yahoo Japan, Baidu, Facebook, Engine Track, etc.", "enum": ["UNSPECIFIED", "UNKNOWN", "BAIDU", "ENGINE_TRACK", "FACEBOOK", "FACEBOOK_GATEWAY", "GOOGLE_ADS", "MICROSOFT", "SEARCH_ADS_360", "YAHOO_JAPAN"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Baidu account.", "Engine track account.", "Facebook account.", "Facebook account managed through gateway.", "Google Ads account.", "Microsoft Advertising account.", "Search Ads 360 manager account.", "Yahoo Japan account."], "readOnly": true, "type": "string"}, "autoTaggingEnabled": {"description": "Whether auto-tagging is enabled for the customer.", "type": "boolean"}, "conversionTrackingSetting": {"$ref": "GoogleAdsSearchads360V0Resources__ConversionTrackingSetting", "description": "Output only. Conversion tracking setting for a customer.", "readOnly": true}, "creationTime": {"description": "Output only. The timestamp when this customer was created. The timestamp is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss\" format.", "readOnly": true, "type": "string"}, "currencyCode": {"description": "Immutable. The currency in which the account operates. A subset of the currency codes from the ISO 4217 standard is supported.", "type": "string"}, "descriptiveName": {"description": "Optional, non-unique descriptive name of the customer.", "type": "string"}, "doubleClickCampaignManagerSetting": {"$ref": "GoogleAdsSearchads360V0Resources__DoubleClickCampaignManagerSetting", "description": "Output only. DoubleClick Campaign Manager (DCM) setting for a manager customer.", "readOnly": true}, "engineId": {"description": "Output only. ID of the account in the external engine account.", "readOnly": true, "type": "string"}, "finalUrlSuffix": {"description": "The URL template for appending params to the final URL.", "type": "string"}, "id": {"description": "Output only. The ID of the customer.", "format": "int64", "readOnly": true, "type": "string"}, "lastModifiedTime": {"description": "Output only. The datetime when this customer was last modified. The datetime is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss.ssssss\" format.", "readOnly": true, "type": "string"}, "manager": {"description": "Output only. Whether the customer is a manager.", "readOnly": true, "type": "boolean"}, "resourceName": {"description": "Immutable. The resource name of the customer. Customer resource names have the form: `customers/{customer_id}`", "type": "string"}, "status": {"description": "Output only. The status of the customer.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "CANCELED", "SUSPENDED", "CLOSED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Indicates an active account able to serve ads.", "Indicates a canceled account unable to serve ads. Can be reactivated by an admin user.", "Indicates a suspended account unable to serve ads. May only be activated by Google support.", "Indicates a closed account unable to serve ads. Test account will also have CLOSED status. Status is permanent and may not be reopened."], "readOnly": true, "type": "string"}, "timeZone": {"description": "Immutable. The local timezone ID of the customer.", "type": "string"}, "trackingUrlTemplate": {"description": "The URL template for constructing a tracking URL out of parameters.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CustomerAsset": {"description": "A link between a customer and an asset.", "id": "GoogleAdsSearchads360V0Resources__CustomerAsset", "properties": {"asset": {"description": "Required. Immutable. The asset which is linked to the customer.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the customer asset. CustomerAsset resource names have the form: `customers/{customer_id}/customerAssets/{asset_id}~{field_type}`", "type": "string"}, "status": {"description": "Status of the customer asset.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED", "PAUSED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Asset link is enabled.", "Asset link has been removed.", "Asset link is paused."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CustomerAssetSet": {"description": "CustomerAssetSet is the linkage between a customer and an asset set. Adding a CustomerAssetSet links an asset set with a customer.", "id": "GoogleAdsSearchads360V0Resources__CustomerAssetSet", "properties": {"assetSet": {"description": "Immutable. The asset set which is linked to the customer.", "type": "string"}, "customer": {"description": "Immutable. The customer to which this asset set is linked.", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the customer asset set. Asset set asset resource names have the form: `customers/{customer_id}/customerAssetSets/{asset_set_id}`", "type": "string"}, "status": {"description": "Output only. The status of the customer asset set asset. Read-only.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED"], "enumDescriptions": ["The status has not been specified.", "The received value is not known in this version. This is a response-only value.", "The linkage between asset set and its container is enabled.", "The linkage between asset set and its container is removed."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CustomerClient": {"description": "A link between the given customer and a client customer. CustomerClients only exist for manager customers. All direct and indirect client customers are included, as well as the manager itself.", "id": "GoogleAdsSearchads360V0Resources__CustomerClient", "properties": {"appliedLabels": {"description": "Output only. The resource names of the labels owned by the requesting customer that are applied to the client customer. Label resource names have the form: `customers/{customer_id}/labels/{label_id}`", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "clientCustomer": {"description": "Output only. The resource name of the client-customer which is linked to the given customer. Read only.", "readOnly": true, "type": "string"}, "currencyCode": {"description": "Output only. Currency code (for example, 'USD', 'EUR') for the client. Read only.", "readOnly": true, "type": "string"}, "descriptiveName": {"description": "Output only. Descriptive name for the client. Read only.", "readOnly": true, "type": "string"}, "hidden": {"description": "Output only. Specifies whether this is a hidden account. Read only.", "readOnly": true, "type": "boolean"}, "id": {"description": "Output only. The ID of the client customer. Read only.", "format": "int64", "readOnly": true, "type": "string"}, "level": {"description": "Output only. Distance between given customer and client. For self link, the level value will be 0. Read only.", "format": "int64", "readOnly": true, "type": "string"}, "manager": {"description": "Output only. Identifies if the client is a manager. Read only.", "readOnly": true, "type": "boolean"}, "resourceName": {"description": "Output only. The resource name of the customer client. CustomerClient resource names have the form: `customers/{customer_id}/customerClients/{client_customer_id}`", "readOnly": true, "type": "string"}, "status": {"description": "Output only. The status of the client customer. Read only.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "CANCELED", "SUSPENDED", "CLOSED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Indicates an active account able to serve ads.", "Indicates a canceled account unable to serve ads. Can be reactivated by an admin user.", "Indicates a suspended account unable to serve ads. May only be activated by Google support.", "Indicates a closed account unable to serve ads. Test account will also have CLOSED status. Status is permanent and may not be reopened."], "readOnly": true, "type": "string"}, "testAccount": {"description": "Output only. Identifies if the client is a test account. Read only.", "readOnly": true, "type": "boolean"}, "timeZone": {"description": "Output only. Common Locale Data Repository (CLDR) string representation of the time zone of the client, for example, America/Los_Angeles. Read only.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__CustomerManagerLink": {"description": "Represents customer-manager link relationship.", "id": "GoogleAdsSearchads360V0Resources__CustomerManagerLink", "properties": {"managerCustomer": {"description": "Output only. The manager customer linked to the customer.", "readOnly": true, "type": "string"}, "managerLinkId": {"description": "Output only. ID of the customer-manager link. This field is read only.", "format": "int64", "readOnly": true, "type": "string"}, "resourceName": {"description": "Immutable. Name of the resource. CustomerManagerLink resource names have the form: `customers/{customer_id}/customerManagerLinks/{manager_customer_id}~{manager_link_id}`", "type": "string"}, "status": {"description": "Status of the link between the customer and the manager.", "enum": ["UNSPECIFIED", "UNKNOWN", "ACTIVE", "INACTIVE", "PENDING", "REFUSED", "CANCELED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Indicates current in-effect relationship", "Indicates terminated relationship", "Indicates relationship has been requested by manager, but the client hasn't accepted yet.", "Relationship was requested by the manager, but the client has refused.", "Indicates relationship has been requested by manager, but manager canceled it."], "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__DoubleClickCampaignManagerSetting": {"description": "DoubleClick Campaign Manager (DCM) setting for a manager customer.", "id": "GoogleAdsSearchads360V0Resources__DoubleClickCampaignManagerSetting", "properties": {"advertiserId": {"description": "Output only. ID of the Campaign Manager advertiser associated with this customer.", "format": "int64", "readOnly": true, "type": "string"}, "networkId": {"description": "Output only. ID of the Campaign Manager network associated with this customer.", "format": "int64", "readOnly": true, "type": "string"}, "timeZone": {"description": "Output only. Time zone of the Campaign Manager network associated with this customer in IANA Time Zone Database format, such as America/New_York.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__DynamicSearchAdsSearchTermView": {"description": "A dynamic search ads search term view.", "id": "GoogleAdsSearchads360V0Resources__DynamicSearchAdsSearchTermView", "properties": {"landingPage": {"description": "Output only. The dynamically selected landing page URL of the impression. This field is read-only.", "readOnly": true, "type": "string"}, "resourceName": {"description": "Output only. The resource name of the dynamic search ads search term view. Dynamic search ads search term view resource names have the form: `customers/{customer_id}/dynamicSearchAdsSearchTermViews/{ad_group_id}~{search_term_fingerprint}~{headline_fingerprint}~{landing_page_fingerprint}~{page_url_fingerprint}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__GenderView": {"description": "A gender view.", "id": "GoogleAdsSearchads360V0Resources__GenderView", "properties": {"resourceName": {"description": "Output only. The resource name of the gender view. Gender view resource names have the form: `customers/{customer_id}/genderViews/{ad_group_id}~{criterion_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__GeoTargetConstant": {"description": "A geo target constant.", "id": "GoogleAdsSearchads360V0Resources__GeoTargetConstant", "properties": {"canonicalName": {"description": "Output only. The fully qualified English name, consisting of the target's name and that of its parent and country.", "readOnly": true, "type": "string"}, "countryCode": {"description": "Output only. The ISO-3166-1 alpha-2 country code that is associated with the target.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. The ID of the geo target constant.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Geo target constant English name.", "readOnly": true, "type": "string"}, "parentGeoTarget": {"description": "Output only. The resource name of the parent geo target constant. Geo target constant resource names have the form: `geoTargetConstants/{parent_geo_target_constant_id}`", "readOnly": true, "type": "string"}, "resourceName": {"description": "Output only. The resource name of the geo target constant. Geo target constant resource names have the form: `geoTargetConstants/{geo_target_constant_id}`", "readOnly": true, "type": "string"}, "status": {"description": "Output only. Geo target constant status.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVAL_PLANNED"], "enumDescriptions": ["No value has been specified.", "The received value is not known in this version. This is a response-only value.", "The geo target constant is valid.", "The geo target constant is obsolete and will be removed."], "readOnly": true, "type": "string"}, "targetType": {"description": "Output only. Geo target constant target type.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__KeywordView": {"description": "A keyword view.", "id": "GoogleAdsSearchads360V0Resources__KeywordView", "properties": {"resourceName": {"description": "Output only. The resource name of the keyword view. Keyword view resource names have the form: `customers/{customer_id}/keywordViews/{ad_group_id}~{criterion_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__Label": {"description": "A label.", "id": "GoogleAdsSearchads360V0Resources__Label", "properties": {"id": {"description": "Output only. ID of the label. Read only.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "The name of the label. This field is required and should not be empty when creating a new label. The length of this string should be between 1 and 80, inclusive.", "type": "string"}, "resourceName": {"description": "Immutable. Name of the resource. Label resource names have the form: `customers/{customer_id}/labels/{label_id}`", "type": "string"}, "status": {"description": "Output only. Status of the label. Read only.", "enum": ["UNSPECIFIED", "UNKNOWN", "ENABLED", "REMOVED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Label is enabled.", "Label is removed."], "readOnly": true, "type": "string"}, "textLabel": {"$ref": "GoogleAdsSearchads360V0Common__TextLabel", "description": "A type of label displaying text on a colored background."}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__LanguageConstant": {"description": "A language.", "id": "GoogleAdsSearchads360V0Resources__LanguageConstant", "properties": {"code": {"description": "Output only. The language code, for example, \"en_US\", \"en_AU\", \"es\", \"fr\", etc.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. The ID of the language constant.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "Output only. The full name of the language in English, for example, \"English (US)\", \"Spanish\", etc.", "readOnly": true, "type": "string"}, "resourceName": {"description": "Output only. The resource name of the language constant. Language constant resource names have the form: `languageConstants/{criterion_id}`", "readOnly": true, "type": "string"}, "targetable": {"description": "Output only. Whether the language is targetable.", "readOnly": true, "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__ListingGroupFilterDimension": {"description": "Listing dimensions for the asset group listing group filter.", "id": "GoogleAdsSearchads360V0Resources__ListingGroupFilterDimension", "properties": {"productBiddingCategory": {"$ref": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductBiddingCategory", "description": "Bidding category of a product offer."}, "productBrand": {"$ref": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductBrand", "description": "Brand of a product offer."}, "productChannel": {"$ref": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductChannel", "description": "Locality of a product offer."}, "productCondition": {"$ref": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductCondition", "description": "Condition of a product offer."}, "productCustomAttribute": {"$ref": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductCustomAttribute", "description": "Custom attribute of a product offer."}, "productItemId": {"$ref": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductItemId", "description": "Item id of a product offer."}, "productType": {"$ref": "GoogleAdsSearchads360V0Resources_ListingGroupFilterDimension_ProductType", "description": "Type of a product offer."}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__ListingGroupFilterDimensionPath": {"description": "The path defining of dimensions defining a listing group filter.", "id": "GoogleAdsSearchads360V0Resources__ListingGroupFilterDimensionPath", "properties": {"dimensions": {"description": "Output only. The complete path of dimensions through the listing group filter hierarchy (excluding the root node) to this listing group filter.", "items": {"$ref": "GoogleAdsSearchads360V0Resources__ListingGroupFilterDimension"}, "readOnly": true, "type": "array"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__LocationView": {"description": "A location view summarizes the performance of campaigns by Location criteria.", "id": "GoogleAdsSearchads360V0Resources__LocationView", "properties": {"resourceName": {"description": "Output only. The resource name of the location view. Location view resource names have the form: `customers/{customer_id}/locationViews/{campaign_id}~{criterion_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__ProductBiddingCategoryConstant": {"description": "A Product Bidding Category.", "id": "GoogleAdsSearchads360V0Resources__ProductBiddingCategoryConstant", "properties": {"countryCode": {"description": "Output only. Two-letter upper-case country code of the product bidding category.", "readOnly": true, "type": "string"}, "id": {"description": "Output only. ID of the product bidding category. This ID is equivalent to the google_product_category ID as described in this article: https://support.google.com/merchants/answer/6324436.", "format": "int64", "readOnly": true, "type": "string"}, "languageCode": {"description": "Output only. Language code of the product bidding category.", "readOnly": true, "type": "string"}, "level": {"description": "Output only. Level of the product bidding category.", "enum": ["UNSPECIFIED", "UNKNOWN", "LEVEL1", "LEVEL2", "LEVEL3", "LEVEL4", "LEVEL5"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "Level 1.", "Level 2.", "Level 3.", "Level 4.", "Level 5."], "readOnly": true, "type": "string"}, "localizedName": {"description": "Output only. Display value of the product bidding category localized according to language_code.", "readOnly": true, "type": "string"}, "productBiddingCategoryConstantParent": {"description": "Output only. Resource name of the parent product bidding category.", "readOnly": true, "type": "string"}, "resourceName": {"description": "Output only. The resource name of the product bidding category. Product bidding category resource names have the form: `productBiddingCategoryConstants/{country_code}~{level}~{id}`", "readOnly": true, "type": "string"}, "status": {"description": "Output only. Status of the product bidding category.", "enum": ["UNSPECIFIED", "UNKNOWN", "ACTIVE", "OBSOLETE"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The category is active and can be used for bidding.", "The category is obsolete. Used only for reporting purposes."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__ProductGroupView": {"description": "A product group view.", "id": "GoogleAdsSearchads360V0Resources__ProductGroupView", "properties": {"resourceName": {"description": "Output only. The resource name of the product group view. Product group view resource names have the form: `customers/{customer_id}/productGroupViews/{ad_group_id}~{criterion_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__SearchAds360Field": {"description": "A field or resource (artifact) used by SearchAds360Service.", "id": "GoogleAdsSearchads360V0Resources__SearchAds360Field", "properties": {"attributeResources": {"description": "Output only. The names of all resources that are selectable with the described artifact. Fields from these resources do not segment metrics when included in search queries. This field is only set for artifacts whose category is RESOURCE.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "category": {"description": "Output only. The category of the artifact.", "enum": ["UNSPECIFIED", "UNKNOWN", "RESOURCE", "ATTRIBUTE", "SEGMENT", "METRIC"], "enumDescriptions": ["Unspecified", "Unknown", "The described artifact is a resource.", "The described artifact is a field and is an attribute of a resource. Including a resource attribute field in a query may segment the query if the resource to which it is attributed segments the resource found in the FROM clause.", "The described artifact is a field and always segments search queries.", "The described artifact is a field and is a metric. It never segments search queries."], "readOnly": true, "type": "string"}, "dataType": {"description": "Output only. This field determines the operators that can be used with the artifact in WHERE clauses.", "enum": ["UNSPECIFIED", "UNKNOWN", "BOOLEAN", "DATE", "DOUBLE", "ENUM", "FLOAT", "INT32", "INT64", "MESSAGE", "RESOURCE_NAME", "STRING", "UINT64"], "enumDescriptions": ["Unspecified", "Unknown", "Maps to google.protobuf.BoolValue Applicable operators: =, !=", "Maps to google.protobuf.StringValue. It can be compared using the set of operators specific to dates however. Applicable operators: =, <, >, <=, >=, BETWEEN, DURING, and IN", "Maps to google.protobuf.DoubleValue Applicable operators: =, !=, <, >, IN, NOT IN", "Maps to an enum. It's specific definition can be found at type_url. Applicable operators: =, !=, IN, NOT IN", "Maps to google.protobuf.FloatValue Applicable operators: =, !=, <, >, IN, NOT IN", "Maps to google.protobuf.Int32Value Applicable operators: =, !=, <, >, <=, >=, BETWEEN, IN, NOT IN", "Maps to google.protobuf.Int64Value Applicable operators: =, !=, <, >, <=, >=, BETWEEN, IN, NOT IN", "Maps to a protocol buffer message type. The data type's details can be found in type_url. No operators work with MESSAGE fields.", "Maps to google.protobuf.StringValue. Represents the resource name (unique id) of a resource or one of its foreign keys. No operators work with RESOURCE_NAME fields.", "Maps to google.protobuf.StringValue. Applicable operators: =, !=, LIKE, NOT LIKE, IN, NOT IN", "Maps to google.protobuf.UInt64Value Applicable operators: =, !=, <, >, <=, >=, BETWEEN, IN, NOT IN"], "readOnly": true, "type": "string"}, "enumValues": {"description": "Output only. Values the artifact can assume if it is a field of type ENUM. This field is only set for artifacts of category SEGMENT or ATTRIBUTE.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "filterable": {"description": "Output only. Whether the artifact can be used in a WHERE clause in search queries.", "readOnly": true, "type": "boolean"}, "isRepeated": {"description": "Output only. Whether the field artifact is repeated.", "readOnly": true, "type": "boolean"}, "metrics": {"description": "Output only. This field lists the names of all metrics that are selectable with the described artifact when it is used in the FROM clause. It is only set for artifacts whose category is RESOURCE.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "name": {"description": "Output only. The name of the artifact.", "readOnly": true, "type": "string"}, "resourceName": {"description": "Output only. The resource name of the artifact. Artifact resource names have the form: `SearchAds360Fields/{name}`", "readOnly": true, "type": "string"}, "segments": {"description": "Output only. This field lists the names of all artifacts, whether a segment or another resource, that segment metrics when included in search queries and when the described artifact is used in the FROM clause. It is only set for artifacts whose category is RESOURCE.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "selectable": {"description": "Output only. Whether the artifact can be used in a SELECT clause in search queries.", "readOnly": true, "type": "boolean"}, "selectableWith": {"description": "Output only. The names of all resources, segments, and metrics that are selectable with the described artifact.", "items": {"type": "string"}, "readOnly": true, "type": "array"}, "sortable": {"description": "Output only. Whether the artifact can be used in a ORDER BY clause in search queries.", "readOnly": true, "type": "boolean"}, "typeUrl": {"description": "Output only. The URL of proto describing the artifact's data type.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__ShoppingPerformanceView": {"description": "Shopping performance view. Provides Shopping campaign statistics aggregated at several product dimension levels. Product dimension values from Merchant Center such as brand, category, custom attributes, product condition and product type will reflect the state of each dimension as of the date and time when the corresponding event was recorded.", "id": "GoogleAdsSearchads360V0Resources__ShoppingPerformanceView", "properties": {"resourceName": {"description": "Output only. The resource name of the Shopping performance view. Shopping performance view resource names have the form: `customers/{customer_id}/shoppingPerformanceView`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__UserList": {"description": "A user list. This is a list of users a customer may target.", "id": "GoogleAdsSearchads360V0Resources__UserList", "properties": {"id": {"description": "Output only. Id of the user list.", "format": "int64", "readOnly": true, "type": "string"}, "name": {"description": "Name of this user list. Depending on its access_reason, the user list name may not be unique (for example, if access_reason=SHARED)", "type": "string"}, "resourceName": {"description": "Immutable. The resource name of the user list. User list resource names have the form: `customers/{customer_id}/userLists/{user_list_id}`", "type": "string"}, "type": {"description": "Output only. Type of this list. This field is read-only.", "enum": ["UNSPECIFIED", "UNKNOWN", "REMARKETING", "LOGICAL", "EXTERNAL_REMARKETING", "RULE_BASED", "SIMILAR", "CRM_BASED"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "UserList represented as a collection of conversion types.", "UserList represented as a combination of other user lists/interests.", "UserList created in the Google Ad Manager platform.", "UserList associated with a rule.", "UserList with users similar to users of another UserList.", "UserList of first-party CRM data provided by advertiser in the form of emails or other formats."], "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__Visit": {"description": "A visit.", "id": "GoogleAdsSearchads360V0Resources__Visit", "properties": {"adId": {"description": "Output only. Ad ID. A value of 0 indicates that the ad is unattributed.", "format": "int64", "readOnly": true, "type": "string"}, "assetFieldType": {"description": "Output only. Asset field type of the visit event.", "enum": ["UNSPECIFIED", "UNKNOWN", "HEADLINE", "DESCRIPTION", "MANDATORY_AD_TEXT", "MARKETING_IMAGE", "MEDIA_BUNDLE", "YOUTUBE_VIDEO", "BOOK_ON_GOOGLE", "LEAD_FORM", "PROMOTION", "CALLOUT", "STRUCTURED_SNIPPET", "SITELINK", "MOBILE_APP", "HOTEL_CALLOUT", "CALL", "PRICE", "LONG_HEADLINE", "BUSINESS_NAME", "SQUARE_MARKETING_IMAGE", "PORTRAIT_MARKETING_IMAGE", "LOGO", "LANDSCAPE_LOGO", "VIDEO", "CALL_TO_ACTION_SELECTION", "AD_IMAGE", "BUSINESS_LOGO", "HOTEL_PROPERTY"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The asset is linked for use as a headline.", "The asset is linked for use as a description.", "The asset is linked for use as mandatory ad text.", "The asset is linked for use as a marketing image.", "The asset is linked for use as a media bundle.", "The asset is linked for use as a YouTube video.", "The asset is linked to indicate that a hotels campaign is \"Book on Google\" enabled.", "The asset is linked for use as a Lead Form extension.", "The asset is linked for use as a Promotion extension.", "The asset is linked for use as a Callout extension.", "The asset is linked for use as a Structured Snippet extension.", "The asset is linked for use as a Sitelink.", "The asset is linked for use as a Mobile App extension.", "The asset is linked for use as a Hotel Callout extension.", "The asset is linked for use as a Call extension.", "The asset is linked for use as a Price extension.", "The asset is linked for use as a long headline.", "The asset is linked for use as a business name.", "The asset is linked for use as a square marketing image.", "The asset is linked for use as a portrait marketing image.", "The asset is linked for use as a logo.", "The asset is linked for use as a landscape logo.", "The asset is linked for use as a non YouTube logo.", "The asset is linked for use to select a call-to-action.", "The asset is linked for use to select an ad image.", "The asset is linked for use as a business logo.", "The asset is linked for use as a hotel property in a Performance Max for travel goals campaign."], "readOnly": true, "type": "string"}, "assetId": {"description": "Output only. ID of the asset which was interacted with during the visit event.", "format": "int64", "readOnly": true, "type": "string"}, "clickId": {"description": "Output only. A unique string for each visit that is passed to the landing page as the click id URL parameter.", "readOnly": true, "type": "string"}, "criterionId": {"description": "Output only. Search Ads 360 keyword ID. A value of 0 indicates that the keyword is unattributed.", "format": "int64", "readOnly": true, "type": "string"}, "id": {"description": "Output only. The ID of the visit.", "format": "int64", "readOnly": true, "type": "string"}, "merchantId": {"description": "Output only. The Search Ads 360 inventory account ID containing the product that was clicked on. Search Ads 360 generates this ID when you link an inventory account in Search Ads 360.", "format": "int64", "readOnly": true, "type": "string"}, "productChannel": {"description": "Output only. The sales channel of the product that was clicked on: Online or Local.", "enum": ["UNSPECIFIED", "UNKNOWN", "ONLINE", "LOCAL"], "enumDescriptions": ["Not specified.", "Used for return value only. Represents value unknown in this version.", "The item is sold online.", "The item is sold in local stores."], "readOnly": true, "type": "string"}, "productCountryCode": {"description": "Output only. The country (ISO-3166 format) registered for the inventory feed that contains the product clicked on.", "readOnly": true, "type": "string"}, "productId": {"description": "Output only. The ID of the product clicked on.", "readOnly": true, "type": "string"}, "productLanguageCode": {"description": "Output only. The language (ISO-639-1) that has been set for the Merchant Center feed containing data about the product.", "readOnly": true, "type": "string"}, "productStoreId": {"description": "Output only. The store in the Local Inventory Ad that was clicked on. This should match the store IDs used in your local products feed.", "readOnly": true, "type": "string"}, "resourceName": {"description": "Output only. The resource name of the visit. Visit resource names have the form: `customers/{customer_id}/visits/{ad_group_id}~{criterion_id}~{ds_visit_id}`", "readOnly": true, "type": "string"}, "visitDateTime": {"description": "Output only. The timestamp of the visit event. The timestamp is in the customer's time zone and in \"yyyy-MM-dd HH:mm:ss\" format.", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Resources__WebpageView": {"description": "A webpage view.", "id": "GoogleAdsSearchads360V0Resources__WebpageView", "properties": {"resourceName": {"description": "Output only. The resource name of the webpage view. Webpage view resource names have the form: `customers/{customer_id}/webpageViews/{ad_group_id}~{criterion_id}`", "readOnly": true, "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Services__CustomColumnHeader": {"description": "Message for custom column header.", "id": "GoogleAdsSearchads360V0Services__CustomColumnHeader", "properties": {"id": {"description": "The custom column ID.", "format": "int64", "type": "string"}, "name": {"description": "The user defined name of the custom column.", "type": "string"}, "referencesMetrics": {"description": "True when the custom column references metrics.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Services__ListAccessibleCustomersResponse": {"description": "Response message for CustomerService.ListAccessibleCustomers.", "id": "GoogleAdsSearchads360V0Services__ListAccessibleCustomersResponse", "properties": {"resourceNames": {"description": "Resource name of customers directly accessible by the user authenticating the call.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "GoogleAdsSearchads360V0Services__ListCustomColumnsResponse": {"description": "Response message for fetching all custom columns associated with a customer.", "id": "GoogleAdsSearchads360V0Services__ListCustomColumnsResponse", "properties": {"customColumns": {"description": "The CustomColumns owned by the provided customer.", "items": {"$ref": "GoogleAdsSearchads360V0Resources__CustomColumn"}, "type": "array"}}, "type": "object"}, "GoogleAdsSearchads360V0Services__SearchAds360Row": {"description": "A returned row from the query.", "id": "GoogleAdsSearchads360V0Services__SearchAds360Row", "properties": {"adGroup": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroup", "description": "The ad group referenced in the query."}, "adGroupAd": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroupAd", "description": "The ad referenced in the query."}, "adGroupAdLabel": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroupAdLabel", "description": "The ad group ad label referenced in the query."}, "adGroupAsset": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroupAsset", "description": "The ad group asset referenced in the query."}, "adGroupAssetSet": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroupAssetSet", "description": "The ad group asset set referenced in the query."}, "adGroupAudienceView": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroupAudienceView", "description": "The ad group audience view referenced in the query."}, "adGroupBidModifier": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroupBidModifier", "description": "The bid modifier referenced in the query."}, "adGroupCriterion": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroupCriterion", "description": "The criterion referenced in the query."}, "adGroupCriterionLabel": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroupCriterionLabel", "description": "The ad group criterion label referenced in the query."}, "adGroupLabel": {"$ref": "GoogleAdsSearchads360V0Resources__AdGroupLabel", "description": "The ad group label referenced in the query."}, "ageRangeView": {"$ref": "GoogleAdsSearchads360V0Resources__AgeRangeView", "description": "The age range view referenced in the query."}, "asset": {"$ref": "GoogleAdsSearchads360V0Resources__Asset", "description": "The asset referenced in the query."}, "assetGroup": {"$ref": "GoogleAdsSearchads360V0Resources__AssetGroup", "description": "The asset group referenced in the query."}, "assetGroupAsset": {"$ref": "GoogleAdsSearchads360V0Resources__AssetGroupAsset", "description": "The asset group asset referenced in the query."}, "assetGroupListingGroupFilter": {"$ref": "GoogleAdsSearchads360V0Resources__AssetGroupListingGroupFilter", "description": "The asset group listing group filter referenced in the query."}, "assetGroupSignal": {"$ref": "GoogleAdsSearchads360V0Resources__AssetGroupSignal", "description": "The asset group signal referenced in the query."}, "assetGroupTopCombinationView": {"$ref": "GoogleAdsSearchads360V0Resources__AssetGroupTopCombinationView", "description": "The asset group top combination view referenced in the query."}, "assetSet": {"$ref": "GoogleAdsSearchads360V0Resources__AssetSet", "description": "The asset set referenced in the query."}, "assetSetAsset": {"$ref": "GoogleAdsSearchads360V0Resources__AssetSetAsset", "description": "The asset set asset referenced in the query."}, "audience": {"$ref": "GoogleAdsSearchads360V0Resources__Audience", "description": "The Audience referenced in the query."}, "biddingStrategy": {"$ref": "GoogleAdsSearchads360V0Resources__BiddingStrategy", "description": "The bidding strategy referenced in the query."}, "campaign": {"$ref": "GoogleAdsSearchads360V0Resources__Campaign", "description": "The campaign referenced in the query."}, "campaignAsset": {"$ref": "GoogleAdsSearchads360V0Resources__CampaignAsset", "description": "The campaign asset referenced in the query."}, "campaignAssetSet": {"$ref": "GoogleAdsSearchads360V0Resources__CampaignAssetSet", "description": "The campaign asset set referenced in the query."}, "campaignAudienceView": {"$ref": "GoogleAdsSearchads360V0Resources__CampaignAudienceView", "description": "The campaign audience view referenced in the query."}, "campaignBudget": {"$ref": "GoogleAdsSearchads360V0Resources__CampaignBudget", "description": "The campaign budget referenced in the query."}, "campaignCriterion": {"$ref": "GoogleAdsSearchads360V0Resources__CampaignCriterion", "description": "The campaign criterion referenced in the query."}, "campaignLabel": {"$ref": "GoogleAdsSearchads360V0Resources__CampaignLabel", "description": "The campaign label referenced in the query."}, "cartDataSalesView": {"$ref": "GoogleAdsSearchads360V0Resources__CartDataSalesView", "description": "The cart data sales view referenced in the query."}, "conversion": {"$ref": "GoogleAdsSearchads360V0Resources__Conversion", "description": "The event level conversion referenced in the query."}, "conversionAction": {"$ref": "GoogleAdsSearchads360V0Resources__ConversionAction", "description": "The conversion action referenced in the query."}, "customColumns": {"description": "The custom columns.", "items": {"$ref": "GoogleAdsSearchads360V0Common__Value"}, "type": "array"}, "customer": {"$ref": "GoogleAdsSearchads360V0Resources__Customer", "description": "The customer referenced in the query."}, "customerAsset": {"$ref": "GoogleAdsSearchads360V0Resources__CustomerAsset", "description": "The customer asset referenced in the query."}, "customerAssetSet": {"$ref": "GoogleAdsSearchads360V0Resources__CustomerAssetSet", "description": "The customer asset set referenced in the query."}, "customerClient": {"$ref": "GoogleAdsSearchads360V0Resources__CustomerClient", "description": "The CustomerClient referenced in the query."}, "customerManagerLink": {"$ref": "GoogleAdsSearchads360V0Resources__CustomerManagerLink", "description": "The CustomerManagerLink referenced in the query."}, "dynamicSearchAdsSearchTermView": {"$ref": "GoogleAdsSearchads360V0Resources__DynamicSearchAdsSearchTermView", "description": "The dynamic search ads search term view referenced in the query."}, "genderView": {"$ref": "GoogleAdsSearchads360V0Resources__GenderView", "description": "The gender view referenced in the query."}, "geoTargetConstant": {"$ref": "GoogleAdsSearchads360V0Resources__GeoTargetConstant", "description": "The geo target constant referenced in the query."}, "keywordView": {"$ref": "GoogleAdsSearchads360V0Resources__KeywordView", "description": "The keyword view referenced in the query."}, "label": {"$ref": "GoogleAdsSearchads360V0Resources__Label", "description": "The label referenced in the query."}, "languageConstant": {"$ref": "GoogleAdsSearchads360V0Resources__LanguageConstant", "description": "The language constant referenced in the query."}, "locationView": {"$ref": "GoogleAdsSearchads360V0Resources__LocationView", "description": "The location view referenced in the query."}, "metrics": {"$ref": "GoogleAdsSearchads360V0Common__Metrics", "description": "The metrics."}, "productBiddingCategoryConstant": {"$ref": "GoogleAdsSearchads360V0Resources__ProductBiddingCategoryConstant", "description": "The Product Bidding Category referenced in the query."}, "productGroupView": {"$ref": "GoogleAdsSearchads360V0Resources__ProductGroupView", "description": "The product group view referenced in the query."}, "segments": {"$ref": "GoogleAdsSearchads360V0Common__Segments", "description": "The segments."}, "shoppingPerformanceView": {"$ref": "GoogleAdsSearchads360V0Resources__ShoppingPerformanceView", "description": "The shopping performance view referenced in the query."}, "userList": {"$ref": "GoogleAdsSearchads360V0Resources__UserList", "description": "The user list referenced in the query."}, "visit": {"$ref": "GoogleAdsSearchads360V0Resources__Visit", "description": "The event level visit referenced in the query."}, "webpageView": {"$ref": "GoogleAdsSearchads360V0Resources__WebpageView", "description": "The webpage view referenced in the query."}}, "type": "object"}, "GoogleAdsSearchads360V0Services__SearchSearchAds360FieldsRequest": {"description": "Request message for SearchAds360FieldService.SearchSearchAds360Fields.", "id": "GoogleAdsSearchads360V0Services__SearchSearchAds360FieldsRequest", "properties": {"pageSize": {"description": "Number of elements to retrieve in a single page. When too large a page is requested, the server may decide to further limit the number of returned resources.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Token of the page to retrieve. If not specified, the first page of results will be returned. Use the value obtained from `next_page_token` in the previous response in order to request the next page of results.", "type": "string"}, "query": {"description": "Required. The query string.", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Services__SearchSearchAds360FieldsResponse": {"description": "Response message for SearchAds360FieldService.SearchSearchAds360Fields.", "id": "GoogleAdsSearchads360V0Services__SearchSearchAds360FieldsResponse", "properties": {"nextPageToken": {"description": "Pagination token used to retrieve the next page of results. Pass the content of this string as the `page_token` attribute of the next request. `next_page_token` is not returned for the last page.", "type": "string"}, "results": {"description": "The list of fields that matched the query.", "items": {"$ref": "GoogleAdsSearchads360V0Resources__SearchAds360Field"}, "type": "array"}, "totalResultsCount": {"description": "Total number of results that match the query ignoring the LIMIT clause.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleAdsSearchads360V0Services__SearchSearchAds360Request": {"description": "Request message for SearchAds360Service.Search.", "id": "GoogleAdsSearchads360V0Services__SearchSearchAds360Request", "properties": {"pageSize": {"description": "Number of elements to retrieve in a single page. When too large a page is requested, the server may decide to further limit the number of returned resources.", "format": "int32", "type": "integer"}, "pageToken": {"description": "Token of the page to retrieve. If not specified, the first page of results will be returned. Use the value obtained from `next_page_token` in the previous response in order to request the next page of results.", "type": "string"}, "query": {"description": "Required. The query string.", "type": "string"}, "returnTotalResultsCount": {"description": "If true, the total number of results that match the query ignoring the LIMIT clause will be included in the response. Default is false.", "type": "boolean"}, "summaryRowSetting": {"description": "Determines whether a summary row will be returned. By default, summary row is not returned. If requested, the summary row will be sent in a response by itself after all other query results are returned.", "enum": ["UNSPECIFIED", "UNKNOWN", "NO_SUMMARY_ROW", "SUMMARY_ROW_WITH_RESULTS", "SUMMARY_ROW_ONLY"], "enumDescriptions": ["Not specified.", "Represent unknown values of return summary row.", "Do not return summary row.", "Return summary row along with results. The summary row will be returned in the last batch alone (last batch will contain no results).", "Return summary row only and return no results."], "type": "string"}, "validateOnly": {"description": "If true, the request is validated but not executed.", "type": "boolean"}}, "type": "object"}, "GoogleAdsSearchads360V0Services__SearchSearchAds360Response": {"description": "Response message for SearchAds360Service.Search.", "id": "GoogleAdsSearchads360V0Services__SearchSearchAds360Response", "properties": {"customColumnHeaders": {"description": "The headers of the custom columns in the results.", "items": {"$ref": "GoogleAdsSearchads360V0Services__CustomColumnHeader"}, "type": "array"}, "fieldMask": {"description": "FieldMask that represents what fields were requested by the user.", "format": "google-fieldmask", "type": "string"}, "nextPageToken": {"description": "Pagination token used to retrieve the next page of results. Pass the content of this string as the `page_token` attribute of the next request. `next_page_token` is not returned for the last page.", "type": "string"}, "results": {"description": "The list of rows that matched the query.", "items": {"$ref": "GoogleAdsSearchads360V0Services__SearchAds360Row"}, "type": "array"}, "summaryRow": {"$ref": "GoogleAdsSearchads360V0Services__SearchAds360Row", "description": "Summary row that contains summary of metrics in results. Summary of metrics means aggregation of metrics across all results, here aggregation could be sum, average, rate, etc."}, "totalResultsCount": {"description": "Total number of results that match the query ignoring the LIMIT clause.", "format": "int64", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Search Ads 360 Reporting API", "version": "v0", "version_module": true}