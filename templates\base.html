<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}GSC Controller App{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block bg-dark sidebar collapse">
                <div class="position-sticky pt-3">
                    <div class="text-center mb-4">
                        <h4 class="text-white">GSC Controller</h4>
                        <small class="text-muted">Google Search Console Dashboard</small>
                    </div>
                    
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link text-white {% if request.endpoint == 'index' %}active{% endif %}" 
                               href="{{ url_for('index') }}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white {% if request.endpoint == 'url_inspection' %}active{% endif %}" 
                               href="{{ url_for('url_inspection') }}">
                                <i class="fas fa-search me-2"></i>
                                URL Inspection & Indexing
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white {% if request.endpoint == 'sitemaps' %}active{% endif %}" 
                               href="{{ url_for('sitemaps') }}">
                                <i class="fas fa-sitemap me-2"></i>
                                Sitemaps
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white {% if request.endpoint == 'robots_txt' %}active{% endif %}" 
                               href="{{ url_for('robots_txt') }}">
                                <i class="fas fa-robot me-2"></i>
                                Robots.txt Management
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link text-white {% if request.endpoint == 'indexing_performance' %}active{% endif %}" 
                               href="{{ url_for('indexing_performance') }}">
                                <i class="fas fa-chart-line me-2"></i>
                                Indexing Performance
                            </a>
                        </li>
                        <li class="nav-item mt-4">
                            <hr class="text-white">
                            <a class="nav-link text-white" href="{{ url_for('logout') }}">
                                <i class="fas fa-sign-out-alt me-2"></i>
                                Logout
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">{% block page_title %}Dashboard{% endblock %}</h1>
                </div>

                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- Page content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
