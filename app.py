import os
import json
from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from dotenv import load_dotenv
import secrets

# Load environment variables
load_dotenv()

app = Flask(__name__)
app.secret_key = os.getenv('FLASK_SECRET_KEY', secrets.token_hex(16))

# Google OAuth 2.0 configuration
GOOGLE_CLIENT_ID = os.getenv('GOOGLE_CLIENT_ID')
GOOGLE_CLIENT_SECRET = os.getenv('GOOGLE_CLIENT_SECRET')
REDIRECT_URI = 'http://127.0.0.1:5000/oauth2callback'

# Google Search Console API scopes
SCOPES = [
    'https://www.googleapis.com/auth/webmasters.readonly',
    'https://www.googleapis.com/auth/webmasters'
]

def get_google_auth_flow():
    """Create and return Google OAuth flow"""
    flow = Flow.from_client_config(
        {
            "web": {
                "client_id": GOOGLE_CLIENT_ID,
                "client_secret": GOOGLE_CLIENT_SECRET,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [REDIRECT_URI]
            }
        },
        scopes=SCOPES
    )
    flow.redirect_uri = REDIRECT_URI
    return flow

def get_gsc_service():
    """Get Google Search Console service using stored credentials"""
    if 'credentials' not in session:
        return None
    
    credentials_info = session['credentials']
    credentials = Credentials.from_authorized_user_info(credentials_info, SCOPES)
    
    if credentials.expired and credentials.refresh_token:
        credentials.refresh(Request())
        session['credentials'] = credentials_to_dict(credentials)
    
    return build('searchconsole', 'v1', credentials=credentials)

def credentials_to_dict(credentials):
    """Convert credentials to dictionary for session storage"""
    return {
        'token': credentials.token,
        'refresh_token': credentials.refresh_token,
        'token_uri': credentials.token_uri,
        'client_id': credentials.client_id,
        'client_secret': credentials.client_secret,
        'scopes': credentials.scopes
    }

@app.route('/')
def index():
    """Main dashboard route"""
    if 'credentials' not in session:
        return redirect(url_for('login'))
    
    service = get_gsc_service()
    if not service:
        return redirect(url_for('login'))
    
    try:
        # Get list of sites
        sites_result = service.sites().list().execute()
        sites = sites_result.get('siteEntry', [])
        
        return render_template('dashboard.html', sites=sites)
    except HttpError as error:
        flash(f'An error occurred: {error}', 'error')
        return render_template('dashboard.html', sites=[])

@app.route('/login')
def login():
    """Login page"""
    return render_template('login.html')

@app.route('/auth')
def auth():
    """Initiate Google OAuth flow"""
    flow = get_google_auth_flow()
    authorization_url, state = flow.authorization_url(
        access_type='offline',
        include_granted_scopes='true'
    )
    session['state'] = state
    return redirect(authorization_url)

@app.route('/oauth2callback')
def oauth2callback():
    """Handle OAuth callback"""
    state = session.get('state')
    if not state or state != request.args.get('state'):
        flash('Invalid state parameter', 'error')
        return redirect(url_for('login'))
    
    flow = get_google_auth_flow()
    flow.fetch_token(authorization_response=request.url)
    
    credentials = flow.credentials
    session['credentials'] = credentials_to_dict(credentials)
    
    return redirect(url_for('index'))

@app.route('/logout')
def logout():
    """Logout and clear session"""
    session.clear()
    flash('You have been logged out successfully', 'success')
    return redirect(url_for('login'))

@app.route('/url-inspection')
def url_inspection():
    """URL Inspection tool"""
    if 'credentials' not in session:
        return redirect(url_for('login'))
    
    return render_template('url_inspection.html')

@app.route('/sitemaps')
def sitemaps():
    """Sitemaps management"""
    if 'credentials' not in session:
        return redirect(url_for('login'))
    
    service = get_gsc_service()
    if not service:
        return redirect(url_for('login'))
    
    # Get selected site from query parameter
    site_url = request.args.get('site')
    sitemaps = []
    
    if site_url:
        try:
            sitemaps_result = service.sitemaps().list(siteUrl=site_url).execute()
            sitemaps = sitemaps_result.get('sitemap', [])
        except HttpError as error:
            flash(f'Error fetching sitemaps: {error}', 'error')
    
    return render_template('sitemaps.html', sitemaps=sitemaps, selected_site=site_url)

@app.route('/indexing-performance')
def indexing_performance():
    """Indexing performance and stats"""
    if 'credentials' not in session:
        return redirect(url_for('login'))
    
    return render_template('indexing_performance.html')

@app.route('/robots-txt')
def robots_txt():
    """Robots.txt management"""
    if 'credentials' not in session:
        return redirect(url_for('login'))
    
    return render_template('robots_txt.html')

if __name__ == '__main__':
    app.run(debug=True, host='127.0.0.1', port=5000)
