{% extends "base.html" %}

{% block title %}Indexing Performance - GSC Controller App{% endblock %}
{% block page_title %}Indexing Performance{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Indexing Overview
                </h5>
            </div>
            <div class="card-body">
                <form id="performanceForm">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="siteUrl" class="form-label">Select Site</label>
                                <select class="form-select" id="siteUrl" name="siteUrl" required>
                                    <option value="">Choose a site...</option>
                                    <!-- Sites will be populated via JavaScript -->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="dateRange" class="form-label">Date Range</label>
                                <select class="form-select" id="dateRange" name="dateRange">
                                    <option value="7">Last 7 days</option>
                                    <option value="30" selected>Last 30 days</option>
                                    <option value="90">Last 90 days</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <div class="mb-3 w-100">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-sync-alt me-2"></i>
                                    Load Data
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4" id="statsSection" style="display: none;">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                <h4 class="card-title text-success" id="indexedPages">-</h4>
                <p class="card-text">Indexed Pages</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                <h4 class="card-title text-warning" id="errorPages">-</h4>
                <p class="card-text">Pages with Errors</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                <h4 class="card-title text-info" id="pendingPages">-</h4>
                <p class="card-text">Pending Pages</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-ban fa-2x text-danger mb-2"></i>
                <h4 class="card-title text-danger" id="excludedPages">-</h4>
                <p class="card-text">Excluded Pages</p>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4" id="chartsSection" style="display: none;">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-area me-2"></i>
                    Indexing Trend
                </h5>
            </div>
            <div class="card-body">
                <canvas id="indexingChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Coverage Status
                </h5>
            </div>
            <div class="card-body">
                <canvas id="coverageChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4" id="detailsSection" style="display: none;">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>
                    Coverage Issues
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="issuesTable">
                        <thead>
                            <tr>
                                <th>Issue Type</th>
                                <th>Affected Pages</th>
                                <th>Status</th>
                                <th>Last Detected</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Issues will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Understanding Indexing Performance
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <h6 class="text-success">Indexed Pages</h6>
                        <p class="small">Pages successfully crawled and added to Google's index. These can appear in search results.</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-warning">Pages with Errors</h6>
                        <p class="small">Pages that couldn't be indexed due to errors like 404s, server errors, or crawl issues.</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-info">Pending Pages</h6>
                        <p class="small">Pages discovered but not yet crawled or processed for indexing.</p>
                    </div>
                    <div class="col-md-3">
                        <h6 class="text-danger">Excluded Pages</h6>
                        <p class="small">Pages intentionally excluded from indexing due to robots.txt, noindex tags, or other directives.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('performanceForm');
    const statsSection = document.getElementById('statsSection');
    const chartsSection = document.getElementById('chartsSection');
    const detailsSection = document.getElementById('detailsSection');
    
    let indexingChart, coverageChart;
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Show sections
        statsSection.style.display = 'block';
        chartsSection.style.display = 'block';
        detailsSection.style.display = 'block';
        
        // Simulate loading data
        loadMockData();
    });
    
    function loadMockData() {
        // Update stats
        document.getElementById('indexedPages').textContent = '1,234';
        document.getElementById('errorPages').textContent = '45';
        document.getElementById('pendingPages').textContent = '12';
        document.getElementById('excludedPages').textContent = '89';
        
        // Create indexing trend chart
        const ctx1 = document.getElementById('indexingChart').getContext('2d');
        if (indexingChart) indexingChart.destroy();
        
        indexingChart = new Chart(ctx1, {
            type: 'line',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: 'Indexed Pages',
                    data: [1100, 1150, 1200, 1234],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: false
                    }
                }
            }
        });
        
        // Create coverage pie chart
        const ctx2 = document.getElementById('coverageChart').getContext('2d');
        if (coverageChart) coverageChart.destroy();
        
        coverageChart = new Chart(ctx2, {
            type: 'doughnut',
            data: {
                labels: ['Indexed', 'Errors', 'Pending', 'Excluded'],
                datasets: [{
                    data: [1234, 45, 12, 89],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(23, 162, 184, 0.8)',
                        'rgba(220, 53, 69, 0.8)'
                    ]
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
        
        // Populate issues table
        const tbody = document.querySelector('#issuesTable tbody');
        tbody.innerHTML = `
            <tr>
                <td><span class="badge bg-danger">404 Not Found</span></td>
                <td>23</td>
                <td><span class="badge bg-warning">Active</span></td>
                <td>2 days ago</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary">View Details</button>
                </td>
            </tr>
            <tr>
                <td><span class="badge bg-warning">Soft 404</span></td>
                <td>12</td>
                <td><span class="badge bg-warning">Active</span></td>
                <td>1 week ago</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary">View Details</button>
                </td>
            </tr>
            <tr>
                <td><span class="badge bg-info">Redirect Error</span></td>
                <td>8</td>
                <td><span class="badge bg-success">Fixed</span></td>
                <td>2 weeks ago</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary">View Details</button>
                </td>
            </tr>
        `;
    }
});
</script>
{% endblock %}
