# GSC Controller App

A comprehensive Flask web application that acts as a custom dashboard for Google Search Console (GSC), providing enhanced functionality for managing your website's SEO performance.

## Features

### Core Functionality
- **Dashboard**: Overview of all verified sites with quick access to tools
- **URL Inspection & Indexing**: Check URL status and request indexing
- **Sitemap Management**: View, submit, and manage sitemaps
- **Robots.txt Management**: Test and analyze robots.txt files
- **Indexing Performance**: View detailed indexing statistics and coverage reports

### Technical Features
- OAuth 2.0 integration with Google Search Console API
- Responsive Bootstrap-based UI
- Real-time data from Google Search Console
- Session management for secure authentication
- Modern, intuitive interface with sidebar navigation

## Setup Instructions

### Prerequisites
- Python 3.7 or higher
- Google Cloud Console account
- Google Search Console access

### 1. Google Cloud Console Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project (e.g., "GSC Controller App")
3. Enable the Google Search Console API:
   - Go to "APIs & Services" → "Enabled APIs & services"
   - Click "+ <PERSON><PERSON><PERSON><PERSON> APIS AND SERVICES"
   - Search for "Google Search Console API" and enable it
4. Create OAuth 2.0 credentials:
   - Go to "APIs & Services" → "Credentials"
   - Click "+ CREATE CREDENTIALS" → "OAuth client ID"
   - Application type: "Web application"
   - Name: "GSC Controller Web Client"
   - Authorized JavaScript origins: `http://127.0.0.1:5000`
   - Authorized redirect URIs: `http://127.0.0.1:5000/oauth2callback`
   - Copy the Client ID and Client Secret

### 2. Project Setup
1. Clone or download this project
2. Create a virtual environment:
   ```bash
   python -m venv venv
   ```
3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - macOS/Linux: `source venv/bin/activate`
4. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### 3. Configuration
1. Update the `.env` file with your Google OAuth credentials:
   ```
   FLASK_APP=app.py
   FLASK_DEBUG=1
   FLASK_SECRET_KEY=your-secret-key-change-this-in-production
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   ```

### 4. Running the Application
1. Activate the virtual environment (if not already active)
2. Run the Flask application:
   ```bash
   python app.py
   ```
3. Open your browser and navigate to `http://127.0.0.1:5000`
4. Click "Sign in with Google" to authenticate with your Google account

## Project Structure

```
GSC Controller App/
├── app.py                 # Main Flask application
├── requirements.txt       # Python dependencies
├── .env                  # Environment variables (configure with your credentials)
├── templates/            # HTML templates
│   ├── base.html         # Base template with sidebar navigation
│   ├── login.html        # Login page
│   ├── dashboard.html    # Main dashboard
│   ├── url_inspection.html    # URL inspection tool
│   ├── sitemaps.html     # Sitemap management
│   ├── robots_txt.html   # Robots.txt management
│   └── indexing_performance.html  # Indexing performance stats
├── static/               # Static assets
│   └── css/
│       └── style.css     # Custom CSS styles
└── README.md            # This file
```

## Usage

### Dashboard
- View all your verified Google Search Console sites
- Quick access to all tools for each site
- Overview cards for main functionality

### URL Inspection
- Enter any URL from your verified sites
- Check indexing status and crawl information
- Request indexing for new or updated pages

### Sitemap Management
- View all submitted sitemaps for a site
- Submit new sitemaps
- Monitor sitemap processing status and statistics

### Robots.txt Management
- Test robots.txt rules against specific URLs
- View current robots.txt content
- Test custom robots.txt configurations

### Indexing Performance
- View indexing statistics and trends
- Monitor coverage issues and errors
- Track indexing performance over time

## Security Notes

- Never commit your `.env` file with real credentials to version control
- Use strong, unique secret keys in production
- Consider using environment variables or secure secret management in production
- The redirect URI in Google Cloud Console must match exactly: `http://127.0.0.1:5000/oauth2callback`

## Future Enhancements

- Integration with Google Gemini AI for SEO insights and recommendations
- Content generation tools powered by AI
- Advanced analytics and reporting
- Bulk operations for URL management
- Export functionality for reports
- Email notifications for indexing issues

## Troubleshooting

### Common Issues
1. **OAuth Error**: Ensure redirect URI matches exactly in Google Cloud Console
2. **API Not Enabled**: Make sure Google Search Console API is enabled in your project
3. **No Sites Found**: Verify you have sites verified in Google Search Console
4. **Permission Errors**: Ensure your Google account has appropriate permissions for the sites

### Support
For issues or questions, please check:
- Google Search Console API documentation
- Flask documentation
- Google OAuth 2.0 documentation

## License

This project is for educational and personal use. Please ensure compliance with Google's API terms of service.
